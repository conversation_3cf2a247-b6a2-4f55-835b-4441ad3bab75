{"name": "fy-app-vite-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:analyze": "npm run build && node scripts/build-analysis.js", "preview": "vite preview"}, "dependencies": {"@types/diff": "^7.0.1", "@vicons/ionicons5": "^0.12.0", "axios": "^1.7.2", "crypto-js": "^4.2.0", "diff": "^7.0.0", "js-base64": "^3.7.7", "moment": "^2.30.1", "prosemirror-commands": "^1.7.0", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.24.1", "prosemirror-schema-basic": "^1.2.3", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.38.1", "qs": "^6.12.1", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "uuid": "^11.0.3", "vue": "^3.4.21", "vue-cookies": "^1.8.4", "vue-draggable-plus": "^0.5.0", "vue-router": "^4.3.3", "xlsx": "^0.18.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^20.14.2", "@types/qs": "^6.9.15", "@vitejs/plugin-vue": "^5.0.4", "naive-ui": "^2.40.1", "typescript": "^5.4.5", "vfonts": "^0.0.3", "vite": "^5.2.0", "vue-tsc": "^2.0.6"}}