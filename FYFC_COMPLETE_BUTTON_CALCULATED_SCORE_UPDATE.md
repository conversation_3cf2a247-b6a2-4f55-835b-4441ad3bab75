# FYFC 评价系统 - 完成按钮更新 calculatedScore 功能

## 📋 功能概述

在 FYFC 评价系统管理员 dashboard 中，当点击"完成"按钮成功更新评价状态为 `completed` 后，系统会自动计算并更新 `calculatedScore`（最终得分）字段。

## ✅ 实现详情

### 1. 新增计算函数

在 `src/views/fyfc/review/admin/dashboard/index.vue` 中添加了 `calculateFinalScore` 函数：

```typescript
// 计算最终得分的辅助函数
const calculateFinalScore = (row: EvaluationTableRow): number => {
    const employeeScore = row.employeeScore || 0;
    const colleagueScore = row.colleagueScore || 0;
    const managerScore = row.managerScore || 0;
    const additionalScore = row.additionalScore || 0;

    // 根据同事评分情况调整比例
    if (colleagueScore === 0) {
        return employeeScore * 0.1 + managerScore * 0.9 + additionalScore;
    } else {
        return employeeScore * 0.1 + colleagueScore * 0.2 + managerScore * 0.7 + additionalScore;
    }
};
```

### 2. 更新 handleComplete 函数

修改了 `handleComplete` 函数的实现逻辑：

```typescript
async function handleComplete(row: EvaluationTableRow) {
    const confirmed = confirm(`确定要将 ${row.name} 的评价状态设置为已完成吗？`);

    if (!confirmed) {
        return;
    }

    try {
        // 先更新状态
        const statusResponse = await fyfcReviewApi.admin.updateEvaluationStatus(row.id!, 'completed', 'admin');

        if (statusResponse.success) {
            // 计算最终得分
            const calculatedScore = calculateFinalScore(row);
            
            // 使用 staff API 更新 calculatedScore
            const updateDto: EvaluationUpdateDto = {
                id: row.id!,
                calculatedScore: calculatedScore,
                updateReason: '管理员完成评价时更新最终得分',
                sendNotification: false
            };
            
            try {
                await fyfcReviewApi.staff.updateEvaluation(updateDto, 'admin');
                console.log('最终得分更新成功:', calculatedScore);
            } catch (updateError) {
                console.warn('更新最终得分失败，但状态更新成功:', updateError);
            }
            
            // 更新本地数据
            const index = evaluationList.value.findIndex(item => item.id === row.id);
            if (index !== -1) {
                evaluationList.value[index].status = 'completed';
                evaluationList.value[index].updatedBy = 'admin';
                evaluationList.value[index].calculatedScore = calculatedScore;
            }
            message.success('状态更新成功');
        } else {
            message.error(statusResponse.message || '状态更新失败');
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        message.error('更新状态失败');
    }
}
```

### 3. 添加必要的导入

```typescript
import { fyfcReviewApi, type EvaluationQueryDto, type EvaluationUpdateDto } from '../../../../../utils/FyfcReviewApi';
```

## 🔄 执行流程

1. **用户点击"完成"按钮**
2. **确认对话框** - 用户确认操作
3. **更新状态** - 调用 `updateEvaluationStatus` API 将状态设为 `completed`
4. **计算最终得分** - 使用 `calculateFinalScore` 函数计算 `calculatedScore`
5. **更新最终得分** - 调用 `updateEvaluation` API 保存 `calculatedScore` 到数据库
6. **更新本地数据** - 同步更新前端表格显示的数据
7. **显示成功消息** - 提示用户操作完成

## 📊 计算公式

### 最终得分计算逻辑

- **当同事评分为 0 时**：
  ```
  calculatedScore = 员工自评 × 10% + 主管评分 × 90% + 线上转发
  ```

- **当同事评分不为 0 时**：
  ```
  calculatedScore = 员工自评 × 10% + 同事评分 × 20% + 主管评分 × 70% + 线上转发
  ```

## 🛡️ 错误处理

- **状态更新失败** - 显示错误消息，不执行后续操作
- **最终得分更新失败** - 记录警告日志，但不影响状态更新的成功
- **网络错误** - 显示通用错误消息

## 🔍 数据同步

### 本地数据更新

成功执行后，本地 `evaluationList` 中对应记录的以下字段会被更新：
- `status` → `'completed'`
- `updatedBy` → `'admin'`
- `calculatedScore` → 计算出的最终得分

### 数据库更新

通过 API 调用，数据库中的以下字段会被更新：
- `status` → `'completed'`
- `calculated_score` → 计算出的最终得分
- `updated_by` → `'admin'`
- `updated_at` → 当前时间戳

## ⚠️ 注意事项

1. **API 依赖** - 功能依赖于后端 `updateEvaluationStatus` 和 `updateEvaluation` API
2. **权限控制** - 只有管理员角色可以执行此操作
3. **数据一致性** - 即使最终得分更新失败，状态更新仍然有效
4. **计算精度** - 最终得分保留小数点后若干位，显示时格式化为1位小数

## 🚀 后续优化建议

1. **原子操作** - 考虑将状态更新和最终得分更新合并为一个 API 调用
2. **批量操作** - 支持批量完成多个评价
3. **审计日志** - 记录管理员操作的详细日志
4. **回滚机制** - 提供撤销"完成"操作的功能
