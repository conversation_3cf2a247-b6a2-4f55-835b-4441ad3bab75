<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Platformer Game</title>
    <style>
        body { margin: 0; display: flex; justify-content: center; align-items: center; height: 100vh; background: #f0f0f0; }
        #canvas { background: #87CEEB; } /* Sky blue background */
        #restart { display: none; padding: 10px 20px; font-size: 20px; cursor: pointer; }
    </style>
</head>
<body>
    <canvas id="canvas" width="800" height="600"></canvas>
    <button id="restart">Restart Game</button>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const restartBtn = document.getElementById('restart');

        let gameOver = false;
        let score = 0;
        let obstacleSpawnTimer = 0;
        let obstacleInterval = 120; // Approx 2 seconds at 60fps

        // Player
        const player = {
            x: 50,
            y: 550,
            width: 50,
            height: 50,
            vx: 0,
            vy: 0,
            onGround: true
        };

        const gravity = 0.5;
        const jumpForce = 15;
        const speed = 5;
        const groundY = canvas.height - player.height;

        // Keys
        const keys = {};
        window.addEventListener('keydown', (e) => { keys[e.key] = true; });
        window.addEventListener('keyup', (e) => { keys[e.key] = false; });

        // Parallax background elements
        const clouds = [];
        function generateCloud() {
            return {
                x: Math.random() * canvas.width,
                y: Math.random() * 150 + 50,
                radius: 40 + Math.random() * 30
            };
        }
        for (let i = 0; i < 8; i++) {
            clouds.push(generateCloud());
        }

        const trees = [];
        function generateTree() {
            return {
                x: Math.random() * canvas.width,
                y: canvas.height,
                width: 20 + Math.random() * 10,
                height: 120 + Math.random() * 60
            };
        }
        for (let i = 0; i < 4; i++) {
            trees.push(generateTree());
        }

        // Coins
        let coins = [];
        function generateCoin() {
            return {
                x: Math.random() * (canvas.width - 40) + 20,
                y: Math.random() * (groundY - 320) + 320, // Adjusted for reachable heights
                radius: 10
            };
        }
        for (let i = 0; i < 5; i++) {
            coins.push(generateCoin());
        }

        // Obstacles
        let obstacles = [];
        function generateObstacle() {
            const height = 30 + Math.random() * 20;
            return {
                x: canvas.width,
                y: canvas.height - height,
                width: 30 + Math.random() * 20,
                height: height,
                vx: -(2 + Math.random() * 2)
            };
        }

        // Collision detection: rect-rect
        function rectRectCollision(a, b) {
            return a.x < b.x + b.width &&
                   a.x + a.width > b.x &&
                   a.y < b.y + b.height &&
                   a.y + a.height > b.y;
        }

        // Collision detection: rect-circle
        function rectCircleCollision(rect, circle) {
            const distX = Math.abs(circle.x - rect.x - rect.width / 2);
            const distY = Math.abs(circle.y - rect.y - rect.height / 2);

            if (distX > (rect.width / 2 + circle.radius)) return false;
            if (distY > (rect.height / 2 + circle.radius)) return false;

            if (distX <= (rect.width / 2)) return true;
            if (distY <= (rect.height / 2)) return true;

            const dx = distX - rect.width / 2;
            const dy = distY - rect.height / 2;
            return (dx * dx + dy * dy <= (circle.radius * circle.radius));
        }

        // Game loop
        function update() {
            if (gameOver) return;

            // Player movement
            player.vx = 0;
            if (keys['ArrowLeft']) player.vx = -speed;
            if (keys['ArrowRight']) player.vx = speed;
            player.x += player.vx;

            // Gravity and jump
            player.vy += gravity;
            player.y += player.vy;

            if (player.y >= groundY) {
                player.y = groundY;
                player.vy = 0;
                player.onGround = true;
            } else {
                player.onGround = false;
            }

            if (keys[' '] && player.onGround) {
                player.vy = -jumpForce;
                player.onGround = false;
            }

            // Boundaries
            if (player.x < 0) player.x = 0;
            if (player.x > canvas.width - player.width) player.x = canvas.width - player.width;

            // Collect coins
            coins = coins.filter(coin => {
                if (rectCircleCollision(player, coin)) {
                    score++;
                    return false;
                }
                return true;
            });
            while (coins.length < 5) {
                coins.push(generateCoin());
            }

            // Spawn obstacles
            obstacleSpawnTimer++;
            if (obstacleSpawnTimer > obstacleInterval) {
                obstacles.push(generateObstacle());
                obstacleSpawnTimer = 0;
                obstacleInterval = 100 + Math.random() * 50; // Vary interval
            }

            // Move obstacles
            obstacles.forEach(obs => {
                obs.x += obs.vx;
            });

            // Remove off-screen obstacles
            obstacles = obstacles.filter(obs => obs.x + obs.width > 0);

            // Check obstacle collisions
            for (let obs of obstacles) {
                if (rectRectCollision(player, obs)) {
                    gameOver = true;
                    showGameOver();
                    return;
                }
            }

            // Move parallax elements
            clouds.forEach(cloud => {
                cloud.x -= 0.5;
                if (cloud.x < -cloud.radius) cloud.x = canvas.width + cloud.radius;
            });

            trees.forEach(tree => {
                tree.x -= 2.5;
                if (tree.x < -tree.width) tree.x = canvas.width + tree.width;
            });

            draw();
            requestAnimationFrame(update);
        }

        // Draw function
        function draw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // Draw clouds (far layer)
            ctx.fillStyle = '#FFF';
            clouds.forEach(cloud => {
                ctx.beginPath();
                ctx.arc(cloud.x, cloud.y, cloud.radius, 0, Math.PI * 2);
                ctx.fill();
            });

            // Draw trees (near layer)
            ctx.fillStyle = 'green';
            trees.forEach(tree => {
                ctx.fillRect(tree.x - tree.width / 2, tree.y - tree.height, tree.width, tree.height);
            });

            // Draw ground
            ctx.fillStyle = 'brown';
            ctx.fillRect(0, canvas.height - 10, canvas.width, 10);

            // Draw player (simple rectangle)
            ctx.fillStyle = 'blue';
            ctx.fillRect(player.x, player.y, player.width, player.height);

            // Draw coins
            ctx.fillStyle = 'yellow';
            coins.forEach(coin => {
                ctx.beginPath();
                ctx.arc(coin.x, coin.y, coin.radius, 0, Math.PI * 2);
                ctx.fill();
            });

            // Draw obstacles
            ctx.fillStyle = 'red';
            obstacles.forEach(obs => {
                ctx.fillRect(obs.x, obs.y, obs.width, obs.height);
            });

            // Draw score
            ctx.fillStyle = 'black';
            ctx.font = '20px Arial';
            ctx.fillText(`Score: ${score}`, 10, 30);
        }

        // Show game over
        function showGameOver() {
            draw(); // Draw final state
            ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = 'white';
            ctx.font = '50px Arial';
            ctx.fillText('GAME OVER', canvas.width / 2 - 150, canvas.height / 2 - 25);
            ctx.font = '30px Arial';
            ctx.fillText(`Final Score: ${score}`, canvas.width / 2 - 100, canvas.height / 2 + 25);
            restartBtn.style.display = 'block';
        }

        // Restart game
        restartBtn.addEventListener('click', () => {
            restartBtn.style.display = 'none';
            gameOver = false;
            score = 0;
            player.x = 50;
            player.y = groundY;
            player.vy = 0;
            player.onGround = true;
            coins = [];
            for (let i = 0; i < 5; i++) coins.push(generateCoin());
            obstacles = [];
            obstacleSpawnTimer = 0;
            obstacleInterval = 120;
            // Reset parallax
            clouds.length = 0;
            for (let i = 0; i < 8; i++) clouds.push(generateCloud());
            trees.length = 0;
            for (let i = 0; i < 4; i++) trees.push(generateTree());
            update();
        });

        update();
    </script>
</body>
</html>