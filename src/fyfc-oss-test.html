<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FYFC OSS 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #666;
        }
        input, button, select {
            padding: 8px 12px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .file-list {
            margin-top: 10px;
        }
        .file-item {
            padding: 10px;
            margin: 5px 0;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .file-actions {
            margin-top: 5px;
        }
        .file-actions button {
            font-size: 12px;
            padding: 4px 8px;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗂️ FYFC OSS 附件服务测试</h1>

        <!-- 健康检查 -->
        <div class="section">
            <h3>🔍 服务健康检查</h3>
            <button onclick="healthCheck()">检查服务状态</button>
            <button onclick="diagnosticCheck()">系统诊断</button>
            <button onclick="configCheck()">配置检查</button>
            <button onclick="uploadConfigCheck()">上传配置检查</button>
            <button onclick="serviceCheck()">服务检查</button>
            <div id="healthResult" class="result"></div>
        </div>

        <!-- 文件上传 -->
        <div class="section">
            <h3>📤 文件上传测试</h3>
            <div>
                <label>选择文件:</label>
                <input type="file" id="uploadFile" multiple>
            </div>
            <div>
                <label>评价ID:</label>
                <input type="number" id="evaluationId" value="1" min="1">
            </div>
            <div>
                <label>上传人:</label>
                <input type="text" id="uploadBy" value="testUser">
            </div>
            <div>
                <label>Bucket名称:</label>
                <input type="text" id="bucketName" placeholder="留空使用默认bucket">
            </div>
            <div>
                <button onclick="uploadFile()">上传单个文件</button>
                <button onclick="uploadFiles()">批量上传</button>
            </div>
            <div id="uploadResult" class="result"></div>
        </div>

        <!-- 获取附件列表 -->
        <div class="section">
            <h3>📋 获取附件列表</h3>
            <div>
                <label>评价ID:</label>
                <input type="number" id="listEvaluationId" value="1" min="1">
                <button onclick="getAttachments()">获取附件列表</button>
                <button onclick="checkEvaluationData()">检查评价数据</button>
            </div>
            <div id="attachmentsList" class="file-list"></div>
        </div>

        <!-- 文件操作 -->
        <div class="section">
            <h3>🔧 文件操作测试</h3>
            <div>
                <label>文件键:</label>
                <input type="text" id="fileKey" placeholder="输入文件键" style="width: 300px;">
            </div>
            <div>
                <label>Bucket名称:</label>
                <input type="text" id="operationBucketName" placeholder="留空使用默认bucket">
            </div>
            <div>
                <button onclick="getFileUrl()">获取下载链接</button>
                <button onclick="previewFile()">预览文件</button>
                <button onclick="deleteFile()">删除文件</button>
            </div>
            <div id="fileOpResult" class="result"></div>
        </div>
    </div>

    <script>
        // 根据当前页面的协议、主机和端口动态构建 API 基础路径
        const BASE_URL = `${window.location.protocol}//${window.location.host}`;
        const CONTEXT_PATH = '/fyschedule2';

        const API_BASE = `${CONTEXT_PATH}/api/fyfc/oss`;
        const TEST_API_BASE = `${CONTEXT_PATH}/api/fyfc/oss/test`;
        const DIAGNOSTIC_API_BASE = `${CONTEXT_PATH}/api/fyfc/oss/diagnostic`;

        console.log('API 配置:');
        console.log('BASE_URL:', BASE_URL);
        console.log('CONTEXT_PATH:', CONTEXT_PATH);
        console.log('API_BASE:', API_BASE);
        console.log('TEST_API_BASE:', TEST_API_BASE);

        // 显示结果
        function showResult(elementId, data, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(data, null, 2);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
        }

        // 健康检查
        async function healthCheck() {
            try {
                console.log('健康检查请求URL:', `${TEST_API_BASE}/health`);
                const response = await fetch(`${TEST_API_BASE}/health`);

                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);

                if (!response.ok) {
                    const text = await response.text();
                    console.log('错误响应内容:', text);
                    showResult('healthResult', {
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        details: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    console.log('非JSON响应:', text);
                    showResult('healthResult', {
                        error: '服务返回了非JSON响应',
                        contentType: contentType,
                        content: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const data = await response.json();
                showResult('healthResult', data, data.success);
            } catch (error) {
                console.error('健康检查失败:', error);
                showResult('healthResult', { error: error.message }, false);
            }
        }

        // 上传单个文件
        async function uploadFile() {
            const fileInput = document.getElementById('uploadFile');
            const evaluationId = document.getElementById('evaluationId').value;
            const uploadBy = document.getElementById('uploadBy').value;
            const bucketName = document.getElementById('bucketName').value;

            if (!fileInput.files.length) {
                showResult('uploadResult', { error: '请选择文件' }, false);
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('evaluationId', evaluationId);
            formData.append('uploadBy', uploadBy);
            if (bucketName) {
                formData.append('bucketName', bucketName);
            }

            try {
                console.log('上传请求URL:', `${API_BASE}/upload`);
                const response = await fetch(`${API_BASE}/upload`, {
                    method: 'POST',
                    body: formData
                });

                console.log('上传响应状态:', response.status);

                if (!response.ok) {
                    const text = await response.text();
                    console.log('上传错误响应:', text);
                    showResult('uploadResult', {
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        details: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    const text = await response.text();
                    console.log('上传非JSON响应:', text);
                    showResult('uploadResult', {
                        error: '服务返回了非JSON响应',
                        contentType: contentType,
                        content: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const data = await response.json();
                showResult('uploadResult', data, data.success);

                // 如果上传成功，自动刷新附件列表
                if (data.success) {
                    document.getElementById('listEvaluationId').value = evaluationId;
                    setTimeout(getAttachments, 500);
                }
            } catch (error) {
                console.error('上传失败:', error);
                showResult('uploadResult', { error: error.message }, false);
            }
        }

        // 批量上传文件
        async function uploadFiles() {
            const fileInput = document.getElementById('uploadFile');
            const evaluationId = document.getElementById('evaluationId').value;
            const uploadBy = document.getElementById('uploadBy').value;
            const bucketName = document.getElementById('bucketName').value;

            if (!fileInput.files.length) {
                showResult('uploadResult', { error: '请选择文件' }, false);
                return;
            }

            const formData = new FormData();
            Array.from(fileInput.files).forEach(file => {
                formData.append('files', file);
            });
            formData.append('evaluationId', evaluationId);
            formData.append('uploadBy', uploadBy);
            if (bucketName) {
                formData.append('bucketName', bucketName);
            }

            try {
                const response = await fetch(`${API_BASE}/upload/batch`, {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                showResult('uploadResult', data, data.success);
                
                // 如果上传成功，自动刷新附件列表
                if (data.success) {
                    document.getElementById('listEvaluationId').value = evaluationId;
                    setTimeout(getAttachments, 500);
                }
            } catch (error) {
                showResult('uploadResult', { error: error.message }, false);
            }
        }

        // 获取附件列表
        async function getAttachments() {
            const evaluationId = document.getElementById('listEvaluationId').value;

            try {
                const response = await fetch(`${API_BASE}/attachments/${evaluationId}`);
                const data = await response.json();

                if (data.success && data.data) {
                    displayAttachments(data.data);
                } else {
                    document.getElementById('attachmentsList').innerHTML =
                        `<div class="result error">${JSON.stringify(data, null, 2)}</div>`;
                }
            } catch (error) {
                document.getElementById('attachmentsList').innerHTML =
                    `<div class="result error">${error.message}</div>`;
            }
        }

        // 检查评价数据（通过通用服务获取完整的评价信息）
        async function checkEvaluationData() {
            const evaluationId = document.getElementById('listEvaluationId').value;

            try {
                console.log('检查评价数据:', evaluationId);
                const response = await fetch(`${CONTEXT_PATH}/api/fyfc/evaluation/common/${evaluationId}`);

                if (!response.ok) {
                    const text = await response.text();
                    document.getElementById('attachmentsList').innerHTML =
                        `<div class="result error">获取评价数据失败: HTTP ${response.status}<br>${text.substring(0, 200)}</div>`;
                    return;
                }

                const data = await response.json();

                if (data.success && data.data) {
                    const evaluation = data.data;
                    const attachmentInfo = evaluation.attachments || [];

                    document.getElementById('attachmentsList').innerHTML = `
                        <div class="result success">
                            <h4>📊 评价数据检查结果</h4>
                            <p><strong>评价ID:</strong> ${evaluation.id}</p>
                            <p><strong>姓名:</strong> ${evaluation.name}</p>
                            <p><strong>部门:</strong> ${evaluation.department}</p>
                            <p><strong>状态:</strong> ${evaluation.status}</p>
                            <p><strong>附件数量:</strong> ${attachmentInfo.length}</p>
                            ${attachmentInfo.length > 0 ? `
                                <h5>📎 附件列表:</h5>
                                <ul>
                                    ${attachmentInfo.map(att => `
                                        <li>
                                            <strong>${att.fileName}</strong>
                                            (${(att.fileSize / 1024).toFixed(2)} KB)
                                            <br>上传人: ${att.uploadBy}
                                            <br>文件键: ${att.fileKey}
                                            ${att.bucketName ? `<br>Bucket: ${att.bucketName}` : ''}
                                        </li>
                                    `).join('')}
                                </ul>
                            ` : '<p>暂无附件</p>'}
                        </div>
                    `;
                } else {
                    document.getElementById('attachmentsList').innerHTML =
                        `<div class="result error">${JSON.stringify(data, null, 2)}</div>`;
                }
            } catch (error) {
                console.error('检查评价数据失败:', error);
                document.getElementById('attachmentsList').innerHTML =
                    `<div class="result error">检查评价数据失败: ${error.message}</div>`;
            }
        }

        // 显示附件列表
        function displayAttachments(attachments) {
            const container = document.getElementById('attachmentsList');
            
            if (!attachments.length) {
                container.innerHTML = '<div class="result">暂无附件</div>';
                return;
            }

            container.innerHTML = attachments.map(att => `
                <div class="file-item">
                    <div><strong>文件名:</strong> ${att.fileName}</div>
                    <div><strong>大小:</strong> ${(att.fileSize / 1024).toFixed(2)} KB</div>
                    <div><strong>上传人:</strong> ${att.uploadBy}</div>
                    <div><strong>文件键:</strong> ${att.fileKey}</div>
                    ${att.bucketName ? `<div><strong>Bucket:</strong> ${att.bucketName}</div>` : ''}
                    <div class="file-actions">
                        <button onclick="copyFileKey('${att.fileKey}')">复制文件键</button>
                        <button onclick="downloadFile('${att.fileKey}', '${att.fileName}', '${att.bucketName || ''}')">下载</button>
                        <button onclick="deleteFileByKey('${att.fileKey}', ${document.getElementById('listEvaluationId').value}, '${att.bucketName || ''}')">删除</button>
                    </div>
                </div>
            `).join('');
        }

        // 复制文件键
        function copyFileKey(fileKey) {
            navigator.clipboard.writeText(fileKey).then(() => {
                alert('文件键已复制到剪贴板');
                document.getElementById('fileKey').value = fileKey;
            });
        }

        // 获取文件URL
        async function getFileUrl() {
            const fileKey = document.getElementById('fileKey').value;
            const bucketName = document.getElementById('operationBucketName').value;

            if (!fileKey) {
                showResult('fileOpResult', { error: '请输入文件键' }, false);
                return;
            }

            try {
                let url = `${API_BASE}/url?fileKey=${encodeURIComponent(fileKey)}`;
                if (bucketName) {
                    url += `&bucketName=${encodeURIComponent(bucketName)}`;
                }

                const response = await fetch(url);
                const data = await response.json();
                showResult('fileOpResult', data, data.success);
            } catch (error) {
                showResult('fileOpResult', { error: error.message }, false);
            }
        }

        // 预览文件
        function previewFile() {
            const fileKey = document.getElementById('fileKey').value;
            const bucketName = document.getElementById('operationBucketName').value;

            if (!fileKey) {
                showResult('fileOpResult', { error: '请输入文件键' }, false);
                return;
            }

            let url = `${API_BASE}/preview?fileKey=${encodeURIComponent(fileKey)}`;
            if (bucketName) {
                url += `&bucketName=${encodeURIComponent(bucketName)}`;
            }
            window.open(url, '_blank');
        }

        // 下载文件
        async function downloadFile(fileKey, fileName, bucketName) {
            try {
                let url = `${API_BASE}/url?fileKey=${encodeURIComponent(fileKey)}`;
                if (bucketName) {
                    url += `&bucketName=${encodeURIComponent(bucketName)}`;
                }

                const response = await fetch(url);
                const data = await response.json();

                if (data.success) {
                    const link = document.createElement('a');
                    link.href = data.data;
                    link.download = fileName;
                    link.click();
                } else {
                    alert('获取下载链接失败: ' + data.message);
                }
            } catch (error) {
                alert('下载失败: ' + error.message);
            }
        }

        // 删除文件
        async function deleteFile() {
            const fileKey = document.getElementById('fileKey').value;
            const evaluationId = document.getElementById('listEvaluationId').value;
            const bucketName = document.getElementById('operationBucketName').value;

            if (!fileKey) {
                showResult('fileOpResult', { error: '请输入文件键' }, false);
                return;
            }

            if (!confirm('确定要删除这个文件吗？')) {
                return;
            }

            await deleteFileByKey(fileKey, evaluationId, bucketName);
        }

        // 根据文件键删除文件
        async function deleteFileByKey(fileKey, evaluationId, bucketName) {
            try {
                let url = `${API_BASE}/delete?fileKey=${encodeURIComponent(fileKey)}&evaluationId=${evaluationId}&operatorName=testUser`;
                if (bucketName) {
                    url += `&bucketName=${encodeURIComponent(bucketName)}`;
                }

                const response = await fetch(url, { method: 'DELETE' });
                const data = await response.json();

                if (data.success) {
                    alert('删除成功');
                    getAttachments(); // 刷新列表
                } else {
                    alert('删除失败: ' + data.message);
                }
            } catch (error) {
                alert('删除失败: ' + error.message);
            }
        }

        // 系统诊断
        async function diagnosticCheck() {
            try {
                console.log('执行系统诊断...');
                const response = await fetch(`${DIAGNOSTIC_API_BASE}/system`);

                if (!response.ok) {
                    const text = await response.text();
                    showResult('healthResult', {
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        details: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const data = await response.json();
                showResult('healthResult', data, data.success);
            } catch (error) {
                console.error('系统诊断失败:', error);
                showResult('healthResult', { error: error.message }, false);
            }
        }

        // 配置检查
        async function configCheck() {
            try {
                console.log('执行配置检查...');
                const response = await fetch(`${DIAGNOSTIC_API_BASE}/config`);

                if (!response.ok) {
                    const text = await response.text();
                    showResult('healthResult', {
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        details: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const data = await response.json();
                showResult('healthResult', data, data.success);
            } catch (error) {
                console.error('配置检查失败:', error);
                showResult('healthResult', { error: error.message }, false);
            }
        }

        // 上传配置检查
        async function uploadConfigCheck() {
            try {
                console.log('执行上传配置检查...');
                const response = await fetch(`${DIAGNOSTIC_API_BASE}/upload-config`);

                if (!response.ok) {
                    const text = await response.text();
                    showResult('healthResult', {
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        details: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const data = await response.json();
                showResult('healthResult', data, data.success);
            } catch (error) {
                console.error('上传配置检查失败:', error);
                showResult('healthResult', { error: error.message }, false);
            }
        }

        // 服务检查
        async function serviceCheck() {
            try {
                console.log('执行服务检查...');
                const response = await fetch(`${DIAGNOSTIC_API_BASE}/service`);

                if (!response.ok) {
                    const text = await response.text();
                    showResult('healthResult', {
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        details: text.substring(0, 200) + '...'
                    }, false);
                    return;
                }

                const data = await response.json();
                showResult('healthResult', data, data.success);
            } catch (error) {
                console.error('服务检查失败:', error);
                showResult('healthResult', { error: error.message }, false);
            }
        }

        // 页面加载时执行健康检查
        window.onload = function() {
            console.log('页面加载完成，开始自动诊断...');
            diagnosticCheck();
        };
    </script>
</body>
</html>
