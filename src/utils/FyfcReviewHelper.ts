import { useMessage } from 'naive-ui';
import type { ApiResponse } from './FyfcReviewApi';

/**
 * FYFC Review 系统辅助工具
 * 提供通用的错误处理、消息提示、数据转换等功能
 */

// 状态映射
export const statusMap = {
  'SELF': 'self',
  'COLLEAGUE': 'colleague', 
  'MANAGER': 'manager',
  'COMPLETED': 'completed'
} as const;

export const reverseStatusMap = {
  'self': 'SELF',
  'colleague': 'COLLEAGUE',
  'manager': 'MANAGER', 
  'completed': 'COMPLETED'
} as const;

// 状态标签
export const statusLabels = {
  'self': '自评',
  'colleague': '同事评价',
  'manager': '主管评价',
  'completed': '已完成'
} as const;

// 状态颜色
export const statusColors = {
  'self': '#2080f0',
  'colleague': '#f0a020',
  'manager': '#18a058',
  'completed': '#52c41a'
} as const;

// 状态标签类型
export const statusTagTypes = {
  'self': 'primary',
  'colleague': 'warning',
  'manager': 'info',
  'completed': 'success'
} as const;

/**
 * API 响应处理器
 */
export class ApiResponseHandler {
  private message: ReturnType<typeof useMessage>;

  constructor() {
    this.message = useMessage();
  }

  /**
   * 处理 API 响应
   * @param response API 响应
   * @param successMessage 成功消息
   * @param showSuccessMessage 是否显示成功消息
   * @returns 处理结果
   */
  handle<T>(
    response: ApiResponse<T>,
    successMessage?: string,
    showSuccessMessage: boolean = true
  ): { success: boolean; data?: T; error?: string } {
    if (response.success) {
      if (showSuccessMessage && successMessage) {
        this.message.success(successMessage);
      }
      return { success: true, data: response.data };
    } else {
      const errorMessage = response.message || '操作失败';
      this.message.error(errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 处理异步操作
   * @param operation 异步操作
   * @param loadingRef 加载状态引用
   * @param successMessage 成功消息
   * @param errorMessage 错误消息
   */
  async handleAsync<T>(
    operation: () => Promise<ApiResponse<T>>,
    loadingRef?: { value: boolean },
    successMessage?: string,
    errorMessage?: string
  ): Promise<{ success: boolean; data?: T; error?: string }> {
    if (loadingRef) {
      loadingRef.value = true;
    }

    try {
      const response = await operation();
      return this.handle(response, successMessage);
    } catch (error: any) {
      const message = errorMessage || error.message || '网络请求失败';
      this.message.error(message);
      return { success: false, error: message };
    } finally {
      if (loadingRef) {
        loadingRef.value = false;
      }
    }
  }
}

/**
 * 数据转换工具
 */
export class DataConverter {
  /**
   * 转换后端状态到前端状态
   */
  static convertStatusFromBackend(backendStatus: string): string {
    return statusMap[backendStatus as keyof typeof statusMap] || backendStatus.toLowerCase();
  }

  /**
   * 转换前端状态到后端状态
   */
  static convertStatusToBackend(frontendStatus: string): string {
    return reverseStatusMap[frontendStatus as keyof typeof reverseStatusMap] || frontendStatus.toUpperCase();
  }

  /**
   * 格式化日期
   */
  static formatDate(timestamp?: number): string {
    if (!timestamp) return '未设置';
    return new Date(timestamp).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  }

  /**
   * 格式化日期时间
   */
  static formatDateTime(timestamp?: number): string {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * 获取状态标签
   */
  static getStatusLabel(status: string): string {
    return statusLabels[status as keyof typeof statusLabels] || status;
  }

  /**
   * 获取状态颜色
   */
  static getStatusColor(status: string): string {
    return statusColors[status as keyof typeof statusColors] || '#d9d9d9';
  }

  /**
   * 获取状态标签类型
   */
  static getStatusTagType(status: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' {
    return statusTagTypes[status as keyof typeof statusTagTypes] as any || 'default';
  }

  /**
   * 计算评分总分
   */
  static calculateTotalScore(scores: {
    performanceScore?: number;
    attitudeScore?: number;
    abilityScore?: number;
    growthScore?: number;
  }): number {
    const { performanceScore = 0, attitudeScore = 0, abilityScore = 0, growthScore = 0 } = scores;
    return performanceScore + attitudeScore + abilityScore + growthScore;
  }

  /**
   * 验证评分数据
   */
  static validateScoreData(scores: {
    performanceScore?: number;
    attitudeScore?: number;
    abilityScore?: number;
    growthScore?: number;
  }): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const { performanceScore, attitudeScore, abilityScore, growthScore } = scores;

    // 验证工作业绩得分 (0-60)
    if (performanceScore === undefined || performanceScore < 0 || performanceScore > 60) {
      errors.push('工作业绩得分必须在0-60分之间');
    }

    // 验证工作态度得分 (0-10)
    if (attitudeScore === undefined || attitudeScore < 0 || attitudeScore > 10) {
      errors.push('工作态度得分必须在0-10分之间');
    }

    // 验证工作能力得分 (0-10)
    if (abilityScore === undefined || abilityScore < 0 || abilityScore > 10) {
      errors.push('工作能力得分必须在0-10分之间');
    }

    // 验证个人成长得分 (0-10)
    if (growthScore === undefined || growthScore < 0 || growthScore > 10) {
      errors.push('个人成长得分必须在0-10分之间');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

/**
 * 权限检查工具
 */
export class PermissionChecker {
  /**
   * 检查用户是否可以编辑评价
   */
  static canEditEvaluation(evaluation: any, currentUser: string): boolean {
    if (!evaluation || !currentUser) return false;

    // 创建人可以编辑
    if (evaluation.createdBy === currentUser) return true;

    // 根据状态和用户角色判断
    const status = evaluation.status;
    switch (status) {
      case 'self':
        return evaluation.name === currentUser;
      case 'colleague':
        return evaluation.colleagueName === currentUser;
      case 'manager':
        return evaluation.managerName === currentUser;
      default:
        return false;
    }
  }

  /**
   * 检查用户是否可以评分
   */
  static canScore(evaluation: any, currentUser: string): boolean {
    if (!evaluation || !currentUser) return false;

    const status = evaluation.status;
    switch (status) {
      case 'self':
        return evaluation.name === currentUser;
      case 'colleague':
        return evaluation.colleagueName === currentUser;
      case 'manager':
        return evaluation.managerName === currentUser;
      default:
        return false;
    }
  }

  /**
   * 检查用户是否与评价相关
   */
  static isRelatedToEvaluation(evaluation: any, currentUser: string): boolean {
    if (!evaluation || !currentUser) return false;

    return evaluation.name === currentUser ||
           evaluation.colleagueName === currentUser ||
           evaluation.managerName === currentUser ||
           evaluation.createdBy === currentUser;
  }
}

/**
 * 导出工具
 */
export class ExportHelper {
  /**
   * 导出评价数据为 CSV
   */
  static exportToCsv(data: any[], filename: string = 'evaluations.csv'): void {
    if (!data || data.length === 0) {
      const message = useMessage();
      message.warning('没有数据可导出');
      return;
    }

    // 构建 CSV 内容
    const headers = ['ID', '部门', '姓名', '评价日期', '状态', '总分', '创建时间'];
    const csvContent = [
      headers.join(','),
      ...data.map(item => [
        item.id || '',
        item.department || '',
        item.name || '',
        DataConverter.formatDate(item.reviewDate),
        DataConverter.getStatusLabel(item.status),
        item.score || '',
        DataConverter.formatDateTime(item.createdAt)
      ].join(','))
    ].join('\n');

    // 下载文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// 创建全局实例
export const apiHandler = new ApiResponseHandler();
export const dataConverter = DataConverter;
export const permissionChecker = PermissionChecker;
export const exportHelper = ExportHelper;

// 默认导出
export default {
  ApiResponseHandler,
  DataConverter,
  PermissionChecker,
  ExportHelper,
  apiHandler,
  dataConverter,
  permissionChecker,
  exportHelper
};
