/**
 * 多角色用户功能测试用例
 * 用于验证UserContext的多角色支持功能
 */

import { useUserContext } from './UserContext';

// 模拟权限数据
const mockPermissions = {
  employeeOnly: ['fyfc-evaluation-employee'],
  managerOnly: ['fyfc-evaluation-manager'],
  adminOnly: ['fyfc-evaluation-admin'],
  employeeManager: ['fyfc-evaluation-employee', 'fyfc-evaluation-manager'],
  managerAdmin: ['fyfc-evaluation-manager', 'fyfc-evaluation-admin'],
  allRoles: ['fyfc-evaluation-employee', 'fyfc-evaluation-manager', 'fyfc-evaluation-admin']
};

/**
 * 测试多角色用户功能
 */
export function testMultiRoleSupport() {
  console.log('=== 开始测试多角色用户功能 ===');
  
  const { 
    setUser, 
    getUserAllRoles, 
    hasRoles, 
    hasAnyRole, 
    isEmployeeManager,
    getEvaluationRole 
  } = useUserContext();

  // 测试用例1：仅有员工权限
  console.log('\n--- 测试用例1：仅有员工权限 ---');
  setUser({
    username: 'employee001',
    name: '张三',
    role: 'employee',
    roles: ['employee'],
    permissions: mockPermissions.employeeOnly
  });
  
  console.log('所有角色:', getUserAllRoles());
  console.log('是否为员工管理者:', isEmployeeManager.value);
  console.log('主要角色:', getEvaluationRole());
  console.log('是否拥有员工权限:', hasRoles(['employee']));
  console.log('是否拥有管理者权限:', hasRoles(['manager']));

  // 测试用例2：仅有管理者权限
  console.log('\n--- 测试用例2：仅有管理者权限 ---');
  setUser({
    username: 'manager001',
    name: '李四',
    role: 'manager',
    roles: ['manager'],
    permissions: mockPermissions.managerOnly
  });
  
  console.log('所有角色:', getUserAllRoles());
  console.log('是否为员工管理者:', isEmployeeManager.value);
  console.log('主要角色:', getEvaluationRole());
  console.log('是否拥有员工权限:', hasRoles(['employee']));
  console.log('是否拥有管理者权限:', hasRoles(['manager']));

  // 测试用例3：同时拥有员工和管理者权限（重点测试）
  console.log('\n--- 测试用例3：同时拥有员工和管理者权限 ---');
  setUser({
    username: 'empmanager001',
    name: '王五',
    role: 'manager', // 主要角色仍为manager（向后兼容）
    roles: ['employee', 'manager'], // 但拥有两种角色
    permissions: mockPermissions.employeeManager
  });
  
  console.log('所有角色:', getUserAllRoles());
  console.log('是否为员工管理者:', isEmployeeManager.value);
  console.log('主要角色:', getEvaluationRole());
  console.log('是否拥有员工权限:', hasRoles(['employee']));
  console.log('是否拥有管理者权限:', hasRoles(['manager']));
  console.log('是否同时拥有员工和管理者权限:', hasRoles(['employee', 'manager']));
  console.log('是否拥有员工或管理者权限:', hasAnyRole(['employee', 'manager']));

  // 测试用例4：管理员权限
  console.log('\n--- 测试用例4：管理员权限 ---');
  setUser({
    username: 'admin001',
    name: '赵六',
    role: 'admin',
    roles: ['admin'],
    permissions: mockPermissions.adminOnly
  });
  
  console.log('所有角色:', getUserAllRoles());
  console.log('是否为员工管理者:', isEmployeeManager.value);
  console.log('主要角色:', getEvaluationRole());
  console.log('是否拥有管理员权限:', hasRoles(['admin']));

  // 测试用例5：拥有所有权限
  console.log('\n--- 测试用例5：拥有所有权限 ---');
  setUser({
    username: 'superuser001',
    name: '超级用户',
    role: 'admin', // 主要角色为admin
    roles: ['employee', 'manager', 'admin'], // 拥有所有角色
    permissions: mockPermissions.allRoles
  });
  
  console.log('所有角色:', getUserAllRoles());
  console.log('是否为员工管理者:', isEmployeeManager.value);
  console.log('主要角色:', getEvaluationRole());
  console.log('是否同时拥有员工和管理者权限:', hasRoles(['employee', 'manager']));
  console.log('是否拥有所有权限:', hasRoles(['employee', 'manager', 'admin']));

  console.log('\n=== 多角色用户功能测试完成 ===');
}

/**
 * 测试角色判断逻辑
 */
export function testRoleLogic() {
  console.log('\n=== 开始测试角色判断逻辑 ===');
  
  const { getUserAllRoles } = useUserContext();
  
  // 模拟不同场景下的角色判断
  const scenarios = [
    {
      name: '新增评价 - 员工管理者',
      userRoles: ['employee', 'manager'],
      evaluationId: null,
      evaluationName: null,
      currentUser: '王五',
      expected: 'employee'
    },
    {
      name: '编辑自己的评价 - 员工管理者',
      userRoles: ['employee', 'manager'],
      evaluationId: '123',
      evaluationName: '王五',
      currentUser: '王五',
      expected: 'employee'
    },
    {
      name: '编辑他人的评价 - 员工管理者',
      userRoles: ['employee', 'manager'],
      evaluationId: '124',
      evaluationName: '张三',
      currentUser: '王五',
      expected: 'manager'
    }
  ];

  scenarios.forEach(scenario => {
    console.log(`\n--- ${scenario.name} ---`);
    
    const hasEmployeeRole = scenario.userRoles.includes('employee');
    const hasManagerRole = scenario.userRoles.includes('manager');
    const hasAdminRole = scenario.userRoles.includes('admin');
    
    let roleType = 'unknown';
    
    if (hasAdminRole) {
      roleType = 'admin';
    } else if (hasManagerRole && hasEmployeeRole) {
      if (!scenario.evaluationId) {
        roleType = 'employee';
      } else if (scenario.evaluationName) {
        if (scenario.currentUser === scenario.evaluationName) {
          roleType = 'employee';
        } else {
          roleType = 'manager';
        }
      } else {
        roleType = 'manager';
      }
    } else if (hasManagerRole) {
      roleType = 'manager';
    } else if (hasEmployeeRole) {
      if (!scenario.evaluationId) {
        roleType = 'employee';
      } else if (scenario.evaluationName) {
        if (scenario.currentUser === scenario.evaluationName) {
          roleType = 'employee';
        } else {
          roleType = 'colleague';
        }
      } else {
        roleType = 'colleague';
      }
    }
    
    console.log(`用户角色: ${scenario.userRoles.join(', ')}`);
    console.log(`评价ID: ${scenario.evaluationId || '无（新增模式）'}`);
    console.log(`评价对象: ${scenario.evaluationName || '未知'}`);
    console.log(`当前用户: ${scenario.currentUser}`);
    console.log(`判断结果: ${roleType}`);
    console.log(`预期结果: ${scenario.expected}`);
    console.log(`测试结果: ${roleType === scenario.expected ? '✅ 通过' : '❌ 失败'}`);
  });
  
  console.log('\n=== 角色判断逻辑测试完成 ===');
}

// 如果在浏览器环境中，可以通过控制台调用测试
if (typeof window !== 'undefined') {
  (window as any).testMultiRoleSupport = testMultiRoleSupport;
  (window as any).testRoleLogic = testRoleLogic;
  
  console.log('多角色测试函数已注册到全局对象:');
  console.log('- testMultiRoleSupport(): 测试多角色用户功能');
  console.log('- testRoleLogic(): 测试角色判断逻辑');
}
