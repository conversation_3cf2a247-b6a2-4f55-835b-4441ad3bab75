/**
 * UserContext 使用示例
 * 展示如何在应用中使用新的用户上下文管理
 */

import { useUserContext, initUserContext } from './UserContext';

// =====================================================
// 1. 应用启动时初始化用户上下文
// =====================================================

// 在 main.ts 或 App.vue 中调用
export async function initializeApp() {
  try {
    console.log('正在初始化用户上下文...');
    await initUserContext();
    console.log('用户上下文初始化完成');
  } catch (error) {
    console.error('用户上下文初始化失败:', error);
  }
}

// =====================================================
// 2. 在组件中使用用户信息
// =====================================================

export function useUserExample() {
  const {
    user,
    isLoggedIn,
    userName,
    displayName,
    userRole,
    isAdmin,
    isManager,
    hasEvaluationPermission,
    getEvaluationRole
  } = useUserContext();

  // 检查用户是否已登录
  const checkLoginStatus = () => {
    if (isLoggedIn.value) {
      console.log('用户已登录:', displayName.value);
      console.log('用户角色:', userRole.value);
    } else {
      console.log('用户未登录');
    }
  };

  // 检查绩效评价权限
  const checkEvaluationAccess = () => {
    if (hasEvaluationPermission()) {
      const role = getEvaluationRole();
      console.log('用户有绩效评价权限，角色:', role);
      
      switch (role) {
        case 'admin':
          console.log('管理员：可以查看和管理所有评价');
          break;
        case 'manager':
          console.log('主管：可以进行主管评价');
          break;
        case 'employee':
          console.log('员工：可以进行自评和同事评价');
          break;
        default:
          console.log('未知角色');
      }
    } else {
      console.log('用户没有绩效评价权限');
    }
  };

  // 检查特定权限
  const checkSpecificPermissions = () => {
    if (isAdmin.value) {
      console.log('用户是管理员');
    }
    
    if (isManager.value) {
      console.log('用户是主管');
    }
  };

  return {
    checkLoginStatus,
    checkEvaluationAccess,
    checkSpecificPermissions
  };
}

// =====================================================
// 3. 权限守卫示例
// =====================================================

export function createPermissionGuard() {
  const { hasEvaluationPermission, getEvaluationRole } = useUserContext();

  // 路由守卫
  const routeGuard = (to: any, from: any, next: any) => {
    // 检查是否需要绩效评价权限
    if (to.path.startsWith('/fyfc/review')) {
      if (!hasEvaluationPermission()) {
        console.warn('用户没有绩效评价权限，重定向到首页');
        next('/');
        return;
      }

      // 检查特定角色权限
      const role = getEvaluationRole();
      if (to.path.includes('/admin') && role !== 'admin') {
        console.warn('用户没有管理员权限');
        next('/fyfc/review/staff');
        return;
      }

      if (to.path.includes('/manager') && !['manager', 'admin'].includes(role)) {
        console.warn('用户没有主管权限');
        next('/fyfc/review/staff');
        return;
      }
    }

    next();
  };

  return { routeGuard };
}

// =====================================================
// 4. 组件权限控制示例
// =====================================================

export function useComponentPermissions() {
  const { getEvaluationRole, hasEvaluationPermission } = useUserContext();

  // 检查是否可以显示管理员功能
  const canShowAdminFeatures = () => {
    return getEvaluationRole() === 'admin';
  };

  // 检查是否可以显示主管功能
  const canShowManagerFeatures = () => {
    const role = getEvaluationRole();
    return role === 'manager' || role === 'admin';
  };

  // 检查是否可以显示员工功能
  const canShowEmployeeFeatures = () => {
    return hasEvaluationPermission();
  };

  // 检查是否可以编辑特定评价
  const canEditEvaluation = (evaluation: any, currentUser: string) => {
    const role = getEvaluationRole();
    
    // 管理员可以编辑所有评价
    if (role === 'admin') return true;
    
    // 根据评价状态和用户角色判断
    switch (evaluation.status) {
      case 'self':
        return evaluation.name === currentUser;
      case 'colleague':
        return evaluation.colleagueName === currentUser;
      case 'manager':
        return evaluation.managerName === currentUser && ['manager', 'admin'].includes(role);
      default:
        return false;
    }
  };

  return {
    canShowAdminFeatures,
    canShowManagerFeatures,
    canShowEmployeeFeatures,
    canEditEvaluation
  };
}

// =====================================================
// 5. 错误处理和重试机制
// =====================================================

export function useUserContextWithRetry() {
  const { initializeUser } = useUserContext();

  // 重试初始化用户信息
  const retryInitialization = async (maxRetries = 3) => {
    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`尝试初始化用户信息 (${i + 1}/${maxRetries})`);
        const user = await initializeUser();
        
        if (user) {
          console.log('用户信息初始化成功');
          return user;
        }
      } catch (error) {
        console.error(`第 ${i + 1} 次初始化失败:`, error);
        
        if (i < maxRetries - 1) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
      }
    }
    
    console.error('用户信息初始化失败，已达到最大重试次数');
    return null;
  };

  return { retryInitialization };
}

// =====================================================
// 6. Vue 组件中的使用示例
// =====================================================

/*
// 在 Vue 组件中使用
<template>
  <div>
    <div v-if="isLoggedIn">
      <h1>欢迎, {{ displayName }}</h1>
      <p>角色: {{ userRole }}</p>
      
      <!-- 管理员功能 -->
      <div v-if="canShowAdminFeatures()">
        <button @click="goToAdminDashboard">管理员面板</button>
      </div>
      
      <!-- 主管功能 -->
      <div v-if="canShowManagerFeatures()">
        <button @click="goToManagerDashboard">主管面板</button>
      </div>
      
      <!-- 员工功能 -->
      <div v-if="canShowEmployeeFeatures()">
        <button @click="goToStaffDashboard">员工面板</button>
      </div>
    </div>
    
    <div v-else>
      <p>请先登录</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserContext } from '@/utils/UserContext';
import { useComponentPermissions } from '@/utils/UserContext-usage-example';

const {
  isLoggedIn,
  displayName,
  userRole
} = useUserContext();

const {
  canShowAdminFeatures,
  canShowManagerFeatures,
  canShowEmployeeFeatures
} = useComponentPermissions();

// 路由跳转方法
const goToAdminDashboard = () => {
  // 跳转到管理员面板
};

const goToManagerDashboard = () => {
  // 跳转到主管面板
};

const goToStaffDashboard = () => {
  // 跳转到员工面板
};
</script>
*/
