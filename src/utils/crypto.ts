import CryptoJS from 'crypto-js';

// 加密密钥 - 在实际项目中应该从环境变量获取
const SECRET_KEY = 'fyfc-review-secret-key-2024';

/**
 * 加密对象
 * @param data 要加密的数据对象
 * @returns 加密后的字符串
 */
export function encryptData(data: Record<string, any>): string {
    try {
        const jsonString = JSON.stringify(data);
        const encrypted = CryptoJS.AES.encrypt(jsonString, SECRET_KEY).toString();
        // 使用 Base64 URL-safe 编码，避免 URL 中的特殊字符
        return btoa(encrypted).replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    } catch (error) {
        console.error('加密失败:', error);
        return '';
    }
}

/**
 * 解密字符串
 * @param encryptedData 加密的字符串
 * @returns 解密后的数据对象
 */
export function decryptData(encryptedData: string): Record<string, any> | null {
    try {
        // 还原 Base64 URL-safe 编码
        let base64 = encryptedData.replace(/-/g, '+').replace(/_/g, '/');
        // 补齐 padding
        while (base64.length % 4) {
            base64 += '=';
        }
        
        const encrypted = atob(base64);
        const decrypted = CryptoJS.AES.decrypt(encrypted, SECRET_KEY);
        const jsonString = decrypted.toString(CryptoJS.enc.Utf8);
        
        if (!jsonString) {
            throw new Error('解密失败：无效的数据');
        }
        
        return JSON.parse(jsonString);
    } catch (error) {
        console.error('解密失败:', error);
        return null;
    }
}

/**
 * 加密路由参数
 * @param params 路由参数对象
 * @returns 包含加密token的对象
 */
export function encryptRouteParams(params: { id?: number | string; mode?: string; [key: string]: any }): { token: string } {
    const encrypted = encryptData(params);
    return { token: encrypted };
}

/**
 * 解密路由参数
 * @param token 加密的token
 * @returns 解密后的参数对象
 */
export function decryptRouteParams(token: string): { id?: number | string; mode?: string; [key: string]: any } | null {
    return decryptData(token);
}

/**
 * 生成加密的路由URL
 * @param basePath 基础路径
 * @param params 参数对象
 * @returns 完整的加密URL
 */
export function generateEncryptedUrl(basePath: string, params: Record<string, any>): string {
    const encryptedParams = encryptRouteParams(params);
    const searchParams = new URLSearchParams(encryptedParams);
    return `${basePath}?${searchParams.toString()}`;
}

/**
 * 从当前路由解析加密参数
 * @param route Vue Router的route对象
 * @returns 解密后的参数对象
 */
export function parseEncryptedRoute(route: any): Record<string, any> | null {
    const token = route.query.token as string;
    if (!token) {
        console.warn('未找到加密token');
        return null;
    }
    
    return decryptRouteParams(token);
}
