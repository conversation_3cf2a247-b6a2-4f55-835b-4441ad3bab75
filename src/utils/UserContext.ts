import { ref, computed } from 'vue';
import VueCookies from 'vue-cookies';
import { doGetAsync } from './AxiosUtil';

/**
 * 用户上下文管理
 * 提供当前用户信息的全局状态管理
 */

// 用户信息接口
export interface UserInfo {
  id?: string;
  username: string;
  name: string;
  department?: string;
  role?: string; // 主要角色（保持向后兼容）
  roles?: string[]; // 所有角色列表
  permissions?: string[];
  loginTime?: number; // 登录时间戳
  lastActiveTime?: number; // 最后活跃时间戳
}

// 全局用户状态
const currentUser = ref<UserInfo | null>(null);
const isLoggedIn = ref(false);

// 权限代码映射
const PERMISSION_CODES = {
  EMPLOYEE: 'fyfc-evaluation-employee',
  MANAGER: 'fyfc-evaluation-manager',
  ADMIN: 'fyfc-evaluation-admin'
} as const;

// 会话配置
const SESSION_CONFIG = {
  EXPIRE_TIME: 8 * 60 * 60 * 1000, // 8小时过期时间
  INACTIVE_TIME: 2 * 60 * 60 * 1000, // 2小时无活动自动过期
  CHECK_INTERVAL: 5 * 60 * 1000, // 5分钟检查一次
  STORAGE_KEY: 'fyfc_user_info',
  LAST_ACTIVE_KEY: 'fyfc_last_active'
} as const;

/**
 * 获取用户权限信息
 */
async function fetchUserPermissions(): Promise<string[]> {
  try {
    const url = '/aliapi/auth/fe/permission/fyapp?appcode=d6388b546ee444b2888dc2bcb94f1eb9';
    const res: any = await doGetAsync(url, undefined);

    if (res.code === '00000') {
      const elems = res.data.elem;
      const permissions: string[] = [];

      for (const elem of elems) {
        if (elem.code) {
          permissions.push(elem.code);
        }
      }

      // console.log('获取到的权限列表:', permissions);
      return permissions;
    }

    console.warn('获取权限失败:', res);
    return [];
  } catch (error) {
    console.error('获取权限异常:', error);
    return [];
  }
}

/**
 * 根据权限代码确定用户主要角色（保持向后兼容）
 */
function determineUserRole(permissions: string[]): string {
  // 按优先级检查权限（admin > manager > employee）
  if (permissions.includes(PERMISSION_CODES.ADMIN)) {
    return 'admin';
  }
  if (permissions.includes(PERMISSION_CODES.MANAGER)) {
    return 'manager';
  }
  if (permissions.includes(PERMISSION_CODES.EMPLOYEE)) {
    return 'employee';
  }

  // 默认返回unknown
  return 'unknown';
}

/**
 * 获取用户的所有角色
 */
function getUserRoles(permissions: string[]): string[] {
  const roles: string[] = [];

  if (permissions.includes(PERMISSION_CODES.ADMIN)) {
    roles.push('admin');
  }
  if (permissions.includes(PERMISSION_CODES.MANAGER)) {
    roles.push('manager');
  }
  if (permissions.includes(PERMISSION_CODES.EMPLOYEE)) {
    roles.push('employee');
  }

  return roles.length > 0 ? roles : ['unknown'];
}

// 会话管理相关变量
let sessionCheckTimer: NodeJS.Timeout | null = null;

/**
 * 检查会话是否过期
 */
function isSessionExpired(userInfo: UserInfo): boolean {
  const now = Date.now();
  const loginTime = userInfo.loginTime || 0;
  const lastActiveTime = userInfo.lastActiveTime || loginTime;

  // 检查总登录时间是否超过8小时
  if (now - loginTime > SESSION_CONFIG.EXPIRE_TIME) {
    console.log('会话已过期：超过最大登录时间');
    return true;
  }

  // 检查无活动时间是否超过2小时
  if (now - lastActiveTime > SESSION_CONFIG.INACTIVE_TIME) {
    console.log('会话已过期：超过最大无活动时间');
    return true;
  }

  return false;
}

/**
 * 更新最后活跃时间
 */
function updateLastActiveTime(): void {
  if (currentUser.value) {
    const now = Date.now();
    currentUser.value.lastActiveTime = now;

    // 更新localStorage
    localStorage.setItem(SESSION_CONFIG.STORAGE_KEY, JSON.stringify(currentUser.value));
    localStorage.setItem(SESSION_CONFIG.LAST_ACTIVE_KEY, now.toString());
  }
}

/**
 * 启动会话检查定时器
 */
function startSessionCheck(logoutFn: () => void): void {
  if (sessionCheckTimer) {
    clearInterval(sessionCheckTimer);
  }

  sessionCheckTimer = setInterval(() => {
    if (currentUser.value && isSessionExpired(currentUser.value)) {
      console.log('会话已过期，自动注销');
      logoutFn();
    }
  }, SESSION_CONFIG.CHECK_INTERVAL);
}

/**
 * 停止会话检查定时器
 */
function stopSessionCheck(): void {
  if (sessionCheckTimer) {
    clearInterval(sessionCheckTimer);
    sessionCheckTimer = null;
  }
}

/**
 * 用户上下文 Hook
 */
export function useUserContext() {
  // 计算属性
  const user = computed(() => currentUser.value);
  const userName = computed(() => currentUser.value?.username || '');
  const displayName = computed(() => currentUser.value?.name || currentUser.value?.username || '');
  const userDepartment = computed(() => currentUser.value?.department || '');
  const userRole = computed(() => currentUser.value?.role || '');

  // 设置用户信息
  const setUser = (userInfo: UserInfo) => {
    const now = Date.now();
    const userWithTimestamp = {
      ...userInfo,
      loginTime: now,
      lastActiveTime: now
    };

    currentUser.value = userWithTimestamp;
    isLoggedIn.value = true;

    // 保存到 localStorage
    localStorage.setItem(SESSION_CONFIG.STORAGE_KEY, JSON.stringify(userWithTimestamp));
    localStorage.setItem(SESSION_CONFIG.LAST_ACTIVE_KEY, now.toString());

    // console.log('用户信息已设置:', userWithTimestamp);

    // 延迟启动会话检查和活跃度追踪（确保所有函数都已定义）
    setTimeout(() => {
      startUserSessionCheck();

      // 启动活跃度追踪
      if (typeof window !== 'undefined') {
        import('./ActivityTracker').then(({ startActivityTracking }) => {
          startActivityTracking();
        });
      }
    }, 0);
  };

  // 清除用户信息
  const clearUser = () => {
    currentUser.value = null;
    isLoggedIn.value = false;

    // 清除 localStorage
    localStorage.removeItem(SESSION_CONFIG.STORAGE_KEY);
    localStorage.removeItem(SESSION_CONFIG.LAST_ACTIVE_KEY);

    // 停止会话检查
    stopSessionCheck();

    // 停止活跃度追踪
    if (typeof window !== 'undefined') {
      import('./ActivityTracker').then(({ stopActivityTracking }) => {
        stopActivityTracking();
      });
    }

    console.log('用户信息已清除');
  };

  // 注销用户
  const logout = () => {
    console.log('用户注销');
    clearUser();

    // 清除所有相关的Cookie（与login.vue中设置的cookie对应）
    const vueCookies: any = VueCookies;
    vueCookies.remove('easId');      // 清除easId
    vueCookies.remove('account');    // 清除账号信息
    vueCookies.remove('x-token');    // 清除认证token
    vueCookies.remove('historyPath'); // 清除历史路径

    console.log('已清除所有登录相关的Cookie');

    // 跳转到登录页面
    if (typeof window !== 'undefined') {
      // 使用replace避免用户通过后退按钮回到已注销的页面
      window.location.replace('/vfyg/login');
    }
  };

  // 启动会话检查（在logout定义后）
  const startUserSessionCheck = () => {
    startSessionCheck(logout);
  };

  // 从 localStorage 恢复用户信息
  const restoreUser = () => {
    // 首先检查是否有认证token
    const vueCookies: any = VueCookies;
    const token = vueCookies.get('x-token');

    if (!token) {
      console.log('没有认证token，清除本地用户信息');
      clearUser();
      return false;
    }

    try {
      const savedUser = localStorage.getItem(SESSION_CONFIG.STORAGE_KEY);
      if (savedUser) {
        const userInfo = JSON.parse(savedUser);

        // 检查会话是否过期
        if (isSessionExpired(userInfo)) {
          console.log('恢复用户信息时发现会话已过期');
          clearUser();
          return false;
        }

        // 更新最后活跃时间
        const now = Date.now();
        userInfo.lastActiveTime = now;

        currentUser.value = userInfo;
        isLoggedIn.value = true;

        // 更新localStorage中的最后活跃时间
        localStorage.setItem(SESSION_CONFIG.STORAGE_KEY, JSON.stringify(userInfo));
        localStorage.setItem(SESSION_CONFIG.LAST_ACTIVE_KEY, now.toString());

        // console.log('用户信息已恢复:', userInfo);

        // 延迟启动会话检查和活跃度追踪（确保所有函数都已定义）
        setTimeout(() => {
          startUserSessionCheck();

          // 启动活跃度追踪
          if (typeof window !== 'undefined') {
            import('./ActivityTracker').then(({ startActivityTracking }) => {
              startActivityTracking();
            });
          }
        }, 0);
        return true;
      }
    } catch (error) {
      console.error('恢复用户信息失败:', error);
      clearUser();
    }
    return false;
  };

  // 检查权限
  const hasPermission = (permission: string): boolean => {
    if (!currentUser.value?.permissions) return false;
    return currentUser.value.permissions.includes(permission);
  };

  // 检查角色
  const hasRole = (role: string): boolean => {
    return currentUser.value?.role === role;
  };

  // 是否为管理员
  const isAdmin = computed(() => hasRole('admin'));

  // 是否为主管
  const isManager = computed(() => hasRole('manager'));

  // 是否为员工
  const isEmployee = computed(() => hasRole('employee'));

  // 检查是否拥有指定的多个角色
  const hasRoles = (roleList: string[]): boolean => {
    if (!currentUser.value?.roles) return false;
    return roleList.every(role => currentUser.value!.roles!.includes(role));
  };

  // 检查是否拥有指定角色中的任意一个
  const hasAnyRole = (roleList: string[]): boolean => {
    if (!currentUser.value?.roles) return false;
    return roleList.some(role => currentUser.value!.roles!.includes(role));
  };

  // 获取用户的所有角色
  const getUserAllRoles = (): string[] => {
    return currentUser.value?.roles || [];
  };

  // 检查是否同时拥有员工和管理者权限
  const isEmployeeManager = computed(() => hasRoles(['employee', 'manager']));

  // 初始化用户信息（从Cookie和权限接口获取）
  const initializeUser = async (): Promise<UserInfo | null> => {
    try {
      // 从Cookie获取用户名
      const vueCookies: any = VueCookies;
      const username = vueCookies.get('account');

      if (!username) {
        console.warn('未找到用户账号信息');
        return null;
      }

      // 获取用户权限
      const permissions = await fetchUserPermissions();

      // 确定用户角色
      const role = determineUserRole(permissions);
      const roles = getUserRoles(permissions);

      // 构建用户信息
      // 处理用户显示名称：去掉数字，但保留原始用户名作为备用
      const displayName = username.replace(/\d+/g, '').trim();
      const finalDisplayName = displayName || username; // 如果去掉数字后为空，则使用原用户名

      const userInfo: UserInfo = {
        username: username,
        name: finalDisplayName, // 优先使用去掉数字的姓名，如果为空则使用原用户名
        role: role, // 主要角色（保持向后兼容）
        roles: roles, // 所有角色列表
        permissions: permissions
      };

      // 设置用户信息
      setUser(userInfo);

      // console.log('用户初始化成功:', userInfo);
      return userInfo;
    } catch (error) {
      console.error('用户初始化失败:', error);
      return null;
    }
  };

  // 获取当前用户名（用于API调用）
  const getCurrentUsername = (): string => {
    return userName.value || 'anonymous';
  };

  // 获取当前用户显示名称
  const getCurrentDisplayName = (): string => {
    return displayName.value || '未知用户';
  };

  // 检查是否有绩效评价权限
  const hasEvaluationPermission = (): boolean => {
    if (!currentUser.value?.permissions) return false;

    return currentUser.value.permissions.some(permission =>
      permission === PERMISSION_CODES.EMPLOYEE ||
      permission === PERMISSION_CODES.MANAGER ||
      permission === PERMISSION_CODES.ADMIN
    );
  };

  // 获取绩效评价角色
  const getEvaluationRole = (): 'employee' | 'manager' | 'admin' | 'unknown' => {
    if (!currentUser.value?.role) return 'unknown';

    const role = currentUser.value.role;
    if (['employee', 'manager', 'admin'].includes(role)) {
      return role as 'employee' | 'manager' | 'admin';
    }

    return 'unknown';
  };

  // 更新用户活跃时间（用于延长会话）
  const updateActivity = () => {
    updateLastActiveTime();
  };

  // 检查会话状态
  const checkSession = () => {
    if (currentUser.value && isSessionExpired(currentUser.value)) {
      console.log('会话检查：会话已过期');
      logout();
      return false;
    }
    return true;
  };

  // 获取会话剩余时间
  const getSessionRemainingTime = () => {
    if (!currentUser.value) return 0;

    const now = Date.now();
    const loginTime = currentUser.value.loginTime || 0;
    const lastActiveTime = currentUser.value.lastActiveTime || loginTime;

    const maxLoginRemaining = SESSION_CONFIG.EXPIRE_TIME - (now - loginTime);
    const inactiveRemaining = SESSION_CONFIG.INACTIVE_TIME - (now - lastActiveTime);

    return Math.min(maxLoginRemaining, inactiveRemaining);
  };

  return {
    // 状态
    user,
    isLoggedIn,
    userName,
    displayName,
    userDepartment,
    userRole,
    isAdmin,
    isManager,
    isEmployee,
    isEmployeeManager,

    // 方法
    setUser,
    clearUser,
    logout,
    restoreUser,
    hasPermission,
    hasRole,
    hasRoles,
    hasAnyRole,
    getUserAllRoles,
    initializeUser,
    getCurrentUsername,
    getCurrentDisplayName,
    hasEvaluationPermission,
    getEvaluationRole,

    // 会话管理
    updateActivity,
    checkSession,
    getSessionRemainingTime
  };
}

// 初始化用户上下文
export async function initUserContext() {
  // 首先检查是否有认证token
  const vueCookies: any = VueCookies;
  const token = vueCookies.get('x-token');

  if (!token) {
    console.log('没有认证token，跳过用户初始化');
    return;
  }

  const { restoreUser, initializeUser } = useUserContext();

  // 尝试从 localStorage 恢复用户信息
  const restored = restoreUser();

  // 如果没有恢复成功，从Cookie和权限接口初始化用户信息
  if (!restored) {
    console.log('从权限接口初始化用户信息');
    await initializeUser();
  } else {
    // 即使恢复成功，也重新获取最新的权限信息
    console.log('用户信息已恢复，重新获取权限信息');
    await initializeUser();
  }
}

// 导出单例实例
export const userContext = useUserContext();

// 默认导出
export default {
  useUserContext,
  initUserContext,
  userContext
};
