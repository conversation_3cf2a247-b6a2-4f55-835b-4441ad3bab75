import * as XLSX from 'xlsx';
import type { EvaluationTableRow } from '../../views/fyfc/review/admin/dashboard/tableConfig';

// 导出评价数据到 Excel
export const exportEvaluationToExcel = (data: EvaluationTableRow[], filename?: string) => {
    // 准备导出数据
    const exportData = data.map(row => ({
        '部门/项目': row.department || '',
        '姓名': row.name || '',
        '日期': row.reviewDate ? new Date(row.reviewDate).toLocaleDateString('zh-CN') : '',
        '备注': row.comment || '',
        '个人': row.employeeScore?.toFixed(2) || '0.00',
        '同事': row.colleagueScore?.toFixed(2) || '0.00',
        '上级': row.managerScore?.toFixed(2) || '0.00',
        '上级姓名': row.managerEvaluator || '未评分',
        '转发': row.additionalScore?.toFixed(2) || '0.00',
        '合计': row.totalScore?.toFixed(2) || '0.00',
        '最终得分': row.calculatedScore?.toFixed(2) || '0.00',
    }));

    // 创建工作簿
    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '绩效评价数据');

    // 生成文件名
    const defaultFilename = `绩效评价数据_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;
    const finalFilename = filename || defaultFilename;

    // 导出文件
    XLSX.writeFile(wb, finalFilename);

    return {
        success: true,
        filename: finalFilename,
        recordCount: exportData.length
    };
};
