/**
 * 用户活跃度追踪器
 * 自动监听用户活动并更新会话时间
 */

import { userContext } from './UserContext';

// 活跃度追踪配置
const ACTIVITY_CONFIG = {
  UPDATE_INTERVAL: 30 * 1000, // 30秒更新一次活跃时间
  EVENTS: ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'] as const
} as const;

// 追踪器状态
let isTracking = false;
let lastActivityTime = 0;
let activityTimer: NodeJS.Timeout | null = null;

/**
 * 处理用户活动事件
 */
function handleActivity(): void {
  const now = Date.now();
  
  // 避免频繁更新，至少间隔30秒
  if (now - lastActivityTime > ACTIVITY_CONFIG.UPDATE_INTERVAL) {
    lastActivityTime = now;
    
    // 更新用户活跃时间
    if (userContext.isLoggedIn.value) {
      userContext.updateActivity();
      console.log('用户活跃时间已更新');
    }
  }
}

/**
 * 启动活跃度追踪
 */
export function startActivityTracking(): void {
  if (isTracking) {
    console.log('活跃度追踪已经在运行');
    return;
  }

  console.log('启动用户活跃度追踪');
  isTracking = true;
  lastActivityTime = Date.now();

  // 添加事件监听器
  ACTIVITY_CONFIG.EVENTS.forEach(eventType => {
    document.addEventListener(eventType, handleActivity, { passive: true });
  });

  // 启动定时检查（备用机制）
  activityTimer = setInterval(() => {
    if (userContext.isLoggedIn.value) {
      // 检查会话状态
      if (!userContext.checkSession()) {
        console.log('会话检查失败，停止活跃度追踪');
        stopActivityTracking();
      }
    } else {
      console.log('用户未登录，停止活跃度追踪');
      stopActivityTracking();
    }
  }, 60 * 1000); // 每分钟检查一次
}

/**
 * 停止活跃度追踪
 */
export function stopActivityTracking(): void {
  if (!isTracking) {
    console.log('活跃度追踪未在运行');
    return;
  }

  console.log('停止用户活跃度追踪');
  isTracking = false;

  // 移除事件监听器
  ACTIVITY_CONFIG.EVENTS.forEach(eventType => {
    document.removeEventListener(eventType, handleActivity);
  });

  // 清除定时器
  if (activityTimer) {
    clearInterval(activityTimer);
    activityTimer = null;
  }
}

/**
 * 获取追踪状态
 */
export function isActivityTrackingActive(): boolean {
  return isTracking;
}

/**
 * 手动触发活跃时间更新
 */
export function triggerActivityUpdate(): void {
  handleActivity();
}

// 页面卸载时自动停止追踪
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    stopActivityTracking();
  });
}

// 页面可见性变化时的处理
if (typeof document !== 'undefined') {
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      // 页面变为可见时，触发活跃时间更新
      if (isTracking && userContext.isLoggedIn.value) {
        triggerActivityUpdate();
      }
    }
  });
}

export default {
  startActivityTracking,
  stopActivityTracking,
  isActivityTrackingActive,
  triggerActivityUpdate
};
