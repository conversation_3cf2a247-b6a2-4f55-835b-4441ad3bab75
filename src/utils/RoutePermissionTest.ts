/**
 * 路由权限检查测试
 * 验证多角色用户能够正确访问相应的dashboard
 */

/**
 * 模拟路由权限检查逻辑（基于 router/index.ts 的 checkRole 函数）
 */
export function simulateRoutePermissionCheck(params: {
  userRoles: string[];
  requiredRole: 'admin' | 'manager' | 'employee';
}): boolean {
  const { userRoles, requiredRole } = params;
  
  // 模拟 hasAnyRole 函数
  const hasAnyRole = (roleList: string[]): boolean => {
    return roleList.some(role => userRoles.includes(role));
  };

  // 如果要求admin角色，只有拥有admin权限的用户可以访问
  if (requiredRole === 'admin') {
    return userRoles.includes('admin');
  }

  // 如果要求manager角色，拥有admin或manager权限的用户都可以访问
  if (requiredRole === 'manager') {
    return hasAnyRole(['admin', 'manager']);
  }

  // 如果要求employee角色，拥有任意角色权限的用户都可以访问
  if (requiredRole === 'employee') {
    return hasAnyRole(['admin', 'manager', 'employee']);
  }

  return false;
}

/**
 * 测试路由权限检查
 */
export function testRoutePermissions() {
  console.log('=== 开始测试路由权限检查 ===');

  const testCases = [
    {
      name: '多角色用户(employee+manager)访问manager dashboard',
      params: {
        userRoles: ['employee', 'manager'],
        requiredRole: 'manager' as const
      },
      expected: true
    },
    {
      name: '多角色用户(employee+manager)访问staff dashboard',
      params: {
        userRoles: ['employee', 'manager'],
        requiredRole: 'employee' as const
      },
      expected: true
    },
    {
      name: '多角色用户(employee+manager)访问admin dashboard',
      params: {
        userRoles: ['employee', 'manager'],
        requiredRole: 'admin' as const
      },
      expected: false
    },
    {
      name: '单一manager角色访问manager dashboard',
      params: {
        userRoles: ['manager'],
        requiredRole: 'manager' as const
      },
      expected: true
    },
    {
      name: '单一employee角色访问manager dashboard',
      params: {
        userRoles: ['employee'],
        requiredRole: 'manager' as const
      },
      expected: false
    },
    {
      name: '单一employee角色访问staff dashboard',
      params: {
        userRoles: ['employee'],
        requiredRole: 'employee' as const
      },
      expected: true
    },
    {
      name: 'admin角色访问manager dashboard',
      params: {
        userRoles: ['admin'],
        requiredRole: 'manager' as const
      },
      expected: true
    },
    {
      name: 'admin角色访问admin dashboard',
      params: {
        userRoles: ['admin'],
        requiredRole: 'admin' as const
      },
      expected: true
    },
    {
      name: '多角色用户(manager+admin)访问所有dashboard',
      params: {
        userRoles: ['manager', 'admin'],
        requiredRole: 'admin' as const
      },
      expected: true
    },
    {
      name: '无权限用户访问任何dashboard',
      params: {
        userRoles: [],
        requiredRole: 'employee' as const
      },
      expected: false
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n--- 测试用例 ${index + 1}: ${testCase.name} ---`);
    
    const result = simulateRoutePermissionCheck(testCase.params);
    
    console.log('用户角色:', testCase.params.userRoles);
    console.log('要求角色:', testCase.params.requiredRole);
    console.log('预期结果:', testCase.expected);
    console.log('实际结果:', result);
    
    const isTestPassed = result === testCase.expected;
    console.log(`测试结果: ${isTestPassed ? '✅ 通过' : '❌ 失败'}`);
    
    if (!isTestPassed) {
      console.error(`❌ 测试失败: ${testCase.name}`);
      console.error(`  预期: ${testCase.expected}, 实际: ${result}`);
    }
  });

  console.log('\n=== 路由权限检查测试完成 ===');
}

/**
 * 测试具体的dashboard访问场景
 */
export function testDashboardAccess() {
  console.log('\n=== 开始测试Dashboard访问场景 ===');

  const multiRoleUser = ['employee', 'manager'];
  
  const dashboards = [
    { path: '/fyfc/review/admin/dashboard', requiredRole: 'admin' as const, name: 'Admin Dashboard' },
    { path: '/fyfc/review/manager/dashboard', requiredRole: 'manager' as const, name: 'Manager Dashboard' },
    { path: '/fyfc/review/staff/dashboard', requiredRole: 'employee' as const, name: 'Staff Dashboard' }
  ];

  console.log('\n多角色用户(employee+manager)的Dashboard访问权限:');
  dashboards.forEach(dashboard => {
    const canAccess = simulateRoutePermissionCheck({
      userRoles: multiRoleUser,
      requiredRole: dashboard.requiredRole
    });
    
    console.log(`${dashboard.name}: ${canAccess ? '✅ 可访问' : '❌ 不可访问'}`);
  });

  // 重点测试：多角色用户应该能够访问manager dashboard
  const canAccessManagerDashboard = simulateRoutePermissionCheck({
    userRoles: multiRoleUser,
    requiredRole: 'manager'
  });

  console.log(`\n🎯 重点验证: 多角色用户访问Manager Dashboard: ${canAccessManagerDashboard ? '✅ 成功' : '❌ 失败'}`);
  
  if (!canAccessManagerDashboard) {
    console.error('❌ 严重问题: 多角色用户无法访问Manager Dashboard!');
  } else {
    console.log('✅ 验证通过: 多角色用户可以正常访问Manager Dashboard');
  }

  console.log('\n=== Dashboard访问场景测试完成 ===');
}

// 如果在浏览器环境中，可以通过控制台调用测试
if (typeof window !== 'undefined') {
  (window as any).testRoutePermissions = testRoutePermissions;
  (window as any).testDashboardAccess = testDashboardAccess;
  
  console.log('路由权限测试函数已注册到全局对象:');
  console.log('- testRoutePermissions(): 测试路由权限检查逻辑');
  console.log('- testDashboardAccess(): 测试Dashboard访问场景');
}
