import type { FyfcAttachment } from './FyfcReviewApi';

/**
 * FYFC OSS API 响应格式
 */
interface FyfcApiResponse<T> {
  success: boolean;
  code?: number;
  message: string;
  data: T | null;
  timestamp: number;
}

/**
 * FYFC OSS 服务工具类
 */
export class FyfcOssService {
  private readonly baseUrl = '/fyschedule/api/fyfc/oss';
  
  /**
   * 上传单个文件
   */
  async uploadFile(
    file: File,
    evaluationId: number,
    uploadBy: string,
    bucketName?: string
  ): Promise<FyfcAttachment | null> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('evaluationId', evaluationId.toString());
    formData.append('uploadBy', uploadBy);

    // 使用固定的bucket名称
    const finalBucketName = bucketName || 'fyfc';
    formData.append('bucketName', finalBucketName);
    
    try {
      console.log('上传参数:', {
        fileName: file.name,
        fileSize: file.size,
        evaluationId,
        uploadBy,
        bucketName: finalBucketName
      });

      const response = await fetch(`${this.baseUrl}/upload`, {
        method: 'POST',
        body: formData
      });

      console.log('上传响应状态:', response.status, response.statusText);

      if (!response.ok) {
        console.error('上传请求失败:', response.status, response.statusText);
        return null;
      }

      const result: FyfcApiResponse<FyfcAttachment> = await response.json();
      // console.log('上传API响应:', result);

      if (result.success) {
        console.log('上传成功，返回附件信息:', result.data);
        return result.data;
      } else {
        console.error('上传失败，API返回错误:', result.message);
        return null;
      }
    } catch (error) {
      console.error('上传文件失败:', error);
      return null;
    }
  }
  
  /**
   * 批量上传文件
   */
  async uploadFiles(
    files: File[],
    evaluationId: number,
    uploadBy: string,
    bucketName?: string
  ): Promise<FyfcAttachment[]> {
    const formData = new FormData();

    files.forEach(file => {
      formData.append('files', file);
    });

    formData.append('evaluationId', evaluationId.toString());
    formData.append('uploadBy', uploadBy);

    // 使用固定的bucket名称
    const finalBucketName = bucketName || 'fyfc';
    formData.append('bucketName', finalBucketName);
    
    try {
      const response = await fetch(`${this.baseUrl}/upload/batch`, {
        method: 'POST',
        body: formData
      });
      
      const result: FyfcApiResponse<FyfcAttachment[]> = await response.json();
      return result.success ? (result.data || []) : [];
    } catch (error) {
      console.error('批量上传文件失败:', error);
      return [];
    }
  }
  
  /**
   * 获取评价附件列表
   */
  async getEvaluationAttachments(evaluationId: number): Promise<FyfcAttachment[]> {
    try {
      const response = await fetch(`${this.baseUrl}/attachments/${evaluationId}`);
      const result: FyfcApiResponse<FyfcAttachment[]> = await response.json();
      return result.success ? (result.data || []) : [];
    } catch (error) {
      console.error('获取附件列表失败:', error);
      return [];
    }
  }
  
  /**
   * 删除文件
   */
  async deleteFile(
    fileKey: string,
    evaluationId: number,
    operatorName: string,
    bucketName?: string
  ): Promise<boolean> {
    const params = new URLSearchParams({
      fileKey: fileKey,
      evaluationId: evaluationId.toString(),
      operatorName: operatorName
    });

    // 使用固定的bucket名称
    const finalBucketName = bucketName || 'fyfc';
    params.append('bucketName', finalBucketName);
    
    try {
      const response = await fetch(`${this.baseUrl}/delete?${params}`, {
        method: 'DELETE'
      });
      
      const result: FyfcApiResponse<boolean> = await response.json();
      return result.success && (result.data || false);
    } catch (error) {
      console.error('删除文件失败:', error);
      return false;
    }
  }
  
  /**
   * 获取文件下载URL
   */
  async getFileDownloadUrl(
    fileKey: string,
    expireSeconds: number = 3600,
    bucketName?: string
  ): Promise<string | null> {
    const params = new URLSearchParams({
      fileKey: fileKey,
      expireSeconds: expireSeconds.toString()
    });

    // 使用固定的bucket名称
    const finalBucketName = bucketName || 'fyfc';
    params.append('bucketName', finalBucketName);
    
    try {
      const response = await fetch(`${this.baseUrl}/url?${params}`);
      const result: FyfcApiResponse<string> = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('获取下载URL失败:', error);
      return null;
    }
  }
  
  /**
   * 下载文件
   */
  async downloadFile(attachment: FyfcAttachment): Promise<void> {
    try {
      const url = await this.getFileDownloadUrl(
        attachment.fileKey, 
        3600, 
        attachment.bucketName
      );
      
      if (url) {
        const link = document.createElement('a');
        link.href = url;
        link.download = attachment.fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        throw new Error('无法获取下载链接');
      }
    } catch (error) {
      console.error('下载文件失败:', error);
      throw error;
    }
  }
  
  /**
   * 预览文件
   */
  previewFile(fileKey: string, bucketName?: string): void {
    const params = new URLSearchParams({
      fileKey: fileKey,
      expireSeconds: '3600'
    });

    // 使用固定的bucket名称
    const finalBucketName = bucketName || 'fyfc';
    params.append('bucketName', finalBucketName);
    
    window.open(`${this.baseUrl}/preview?${params}`, '_blank');
  }
  
  /**
   * 格式化文件大小
   */
  formatFileSize(sizeInBytes: number): string {
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} B`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(1)} KB`;
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
  }
  
  /**
   * 检查文件类型是否为图片
   */
  isImageFile(fileType: string): boolean {
    return fileType.startsWith('image/');
  }
  
  /**
   * 检查文件类型是否为文档
   */
  isDocumentFile(fileType: string): boolean {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ];
    return documentTypes.includes(fileType);
  }

  /**
   * 获取文件类型图标
   */
  getFileTypeIcon(fileType: string): string {
    if (this.isImageFile(fileType)) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📋';
    return '📎';
  }

  /**
   * 从评价数据中提取附件列表
   */
  extractAttachmentsFromEvaluation(evaluation: { attachments?: string }): FyfcAttachment[] {
    if (!evaluation.attachments) {
      return [];
    }

    try {
      return JSON.parse(evaluation.attachments) as FyfcAttachment[];
    } catch (error) {
      console.error('解析附件数据失败:', error);
      return [];
    }
  }

  /**
   * 统计评价的附件数量
   */
  getAttachmentCount(evaluation: { attachments?: string }): number {
    const attachments = this.extractAttachmentsFromEvaluation(evaluation);
    return attachments.length;
  }

  /**
   * 计算评价附件的总大小
   */
  getTotalAttachmentSize(evaluation: { attachments?: string }): number {
    const attachments = this.extractAttachmentsFromEvaluation(evaluation);
    return attachments.reduce((total, attachment) => total + attachment.fileSize, 0);
  }
}

// 创建单例实例
export const fyfcOssService = new FyfcOssService();
