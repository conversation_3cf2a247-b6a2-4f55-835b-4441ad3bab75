/**
 * 多角色用户在evaluation邀请场景下的角色判断测试
 * 验证多角色用户根据colleagueName和managerName字段被正确识别角色
 */

/**
 * 模拟角色判断逻辑（基于 staff/edit/index.vue 的逻辑）
 */
export function simulateRoleLogic(params: {
  userRoles: string[];
  evaluationId: string | null;
  evaluationName: string | null;
  colleagueName: string | null;
  managerName: string | null;
  currentUser: string;
  pageMode: 'edit' | 'view' | 'create';
}): { roleType: string; canEdit: boolean } {
  const { userRoles, evaluationId, evaluationName, colleagueName, managerName, currentUser, pageMode } = params;
  
  const hasEmployeeRole = userRoles.includes('employee');
  const hasManagerRole = userRoles.includes('manager');
  const hasAdminRole = userRoles.includes('admin');
  
  let roleType = 'unknown';
  let canEdit = false;

  // 分享链接查看模式：允许查看但不允许编辑
  if (pageMode === 'view') {
    if (hasAdminRole) {
      roleType = 'admin';
    } else if (hasManagerRole && hasEmployeeRole) {
      if (!evaluationId) {
        roleType = 'employee';
      } else if (evaluationName) {
        if (currentUser === evaluationName) {
          roleType = 'employee'; // 本人
        } else {
          // 他人的评价：根据evaluation中的邀请字段判断角色
          if (currentUser === colleagueName) {
            roleType = 'colleague'; // 当前用户是被邀请的同事
          } else if (currentUser === managerName) {
            roleType = 'manager'; // 当前用户是被邀请的主管
          } else {
            // 如果都不匹配，默认为manager（向后兼容）
            roleType = 'manager';
          }
        }
      } else {
        // 数据未加载时，默认为manager，等数据加载后会重新计算
        roleType = 'manager';
      }
    } else if (hasManagerRole) {
      roleType = 'manager';
    } else if (hasEmployeeRole) {
      if (!evaluationId) {
        roleType = 'employee';
      } else if (evaluationName) {
        if (currentUser === evaluationName) {
          roleType = 'employee';
        } else {
          roleType = 'colleague';
        }
      } else {
        roleType = 'colleague';
      }
    } else {
      roleType = 'colleague';
    }

    return {
      roleType,
      canEdit: false // 查看模式不允许编辑
    };
  }

  // 编辑模式：检查权限
  if (hasAdminRole) {
    roleType = 'admin';
    canEdit = true;
  } else if (hasManagerRole && hasEmployeeRole) {
    // 同时拥有管理者和员工权限的用户
    if (!evaluationId) {
      // 新增模式：默认为本人自评（员工角色）
      roleType = 'employee';
      canEdit = true;
    } else if (evaluationName) {
      // 编辑模式且数据已加载：根据name判断
      if (currentUser === evaluationName) {
        roleType = 'employee'; // 本人，可以自评
        canEdit = true;
      } else {
        // 他人的评价：根据evaluation中的邀请字段判断角色
        if (currentUser === colleagueName) {
          roleType = 'colleague'; // 当前用户是被邀请的同事
          canEdit = true;
        } else if (currentUser === managerName) {
          roleType = 'manager'; // 当前用户是被邀请的主管
          canEdit = true;
        } else {
          // 如果都不匹配，默认为manager（向后兼容）
          roleType = 'manager';
          canEdit = true;
        }
      }
    } else {
      // 编辑模式但数据未加载：默认为manager，等数据加载后会重新计算
      roleType = 'manager';
      canEdit = true;
    }
  } else if (hasManagerRole) {
    // 仅有管理者权限
    roleType = 'manager';
    canEdit = true;
  } else if (hasEmployeeRole) {
    // 仅有员工权限
    if (!evaluationId) {
      // 新增模式：默认为本人自评
      roleType = 'employee';
      canEdit = true;
    } else if (evaluationName) {
      // 编辑模式且数据已加载：根据name判断
      if (currentUser === evaluationName) {
        roleType = 'employee'; // 本人，可以自评
        canEdit = true;
      } else {
        roleType = 'colleague'; // 他人，同事评价
        canEdit = true;
      }
    } else {
      // 编辑模式但数据未加载：默认为colleague，等数据加载后会重新计算
      roleType = 'colleague';
      canEdit = true;
    }
  }

  return { roleType, canEdit };
}

/**
 * 测试多角色用户在evaluation邀请场景下的角色判断
 */
export function testInvitationRoleLogic() {
  console.log('=== 开始测试多角色用户在evaluation邀请场景下的角色判断 ===');

  const testCases = [
    {
      name: '多角色用户作为被邀请同事访问他人评价（编辑模式）',
      params: {
        userRoles: ['employee', 'manager'],
        evaluationId: '123',
        evaluationName: '张三',
        colleagueName: '王五',
        managerName: '李四',
        currentUser: '王五',
        pageMode: 'edit' as const
      },
      expected: { roleType: 'colleague', canEdit: true }
    },
    {
      name: '多角色用户作为被邀请主管访问他人评价（编辑模式）',
      params: {
        userRoles: ['employee', 'manager'],
        evaluationId: '123',
        evaluationName: '张三',
        colleagueName: '赵六',
        managerName: '王五',
        currentUser: '王五',
        pageMode: 'edit' as const
      },
      expected: { roleType: 'manager', canEdit: true }
    },
    {
      name: '多角色用户作为被邀请同事访问他人评价（查看模式）',
      params: {
        userRoles: ['employee', 'manager'],
        evaluationId: '123',
        evaluationName: '张三',
        colleagueName: '王五',
        managerName: '李四',
        currentUser: '王五',
        pageMode: 'view' as const
      },
      expected: { roleType: 'colleague', canEdit: false }
    },
    {
      name: '多角色用户访问自己的评价',
      params: {
        userRoles: ['employee', 'manager'],
        evaluationId: '123',
        evaluationName: '王五',
        colleagueName: '张三',
        managerName: '李四',
        currentUser: '王五',
        pageMode: 'edit' as const
      },
      expected: { roleType: 'employee', canEdit: true }
    },
    {
      name: '多角色用户既不是同事也不是主管（默认为manager）',
      params: {
        userRoles: ['employee', 'manager'],
        evaluationId: '123',
        evaluationName: '张三',
        colleagueName: '李四',
        managerName: '赵六',
        currentUser: '王五',
        pageMode: 'edit' as const
      },
      expected: { roleType: 'manager', canEdit: true }
    },
    {
      name: '单一员工角色用户作为被邀请同事',
      params: {
        userRoles: ['employee'],
        evaluationId: '123',
        evaluationName: '张三',
        colleagueName: '李四',
        managerName: '王五',
        currentUser: '李四',
        pageMode: 'edit' as const
      },
      expected: { roleType: 'colleague', canEdit: true }
    }
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n--- 测试用例 ${index + 1}: ${testCase.name} ---`);
    
    const result = simulateRoleLogic(testCase.params);
    
    console.log('输入参数:', testCase.params);
    console.log('预期结果:', testCase.expected);
    console.log('实际结果:', result);
    
    const isRoleCorrect = result.roleType === testCase.expected.roleType;
    const isEditCorrect = result.canEdit === testCase.expected.canEdit;
    const isTestPassed = isRoleCorrect && isEditCorrect;
    
    console.log(`角色判断: ${isRoleCorrect ? '✅' : '❌'} (${result.roleType} vs ${testCase.expected.roleType})`);
    console.log(`编辑权限: ${isEditCorrect ? '✅' : '❌'} (${result.canEdit} vs ${testCase.expected.canEdit})`);
    console.log(`测试结果: ${isTestPassed ? '✅ 通过' : '❌ 失败'}`);
  });

  console.log('\n=== 多角色用户邀请场景角色判断测试完成 ===');
}

// 如果在浏览器环境中，可以通过控制台调用测试
if (typeof window !== 'undefined') {
  (window as any).testInvitationRoleLogic = testInvitationRoleLogic;

  console.log('邀请场景测试函数已注册到全局对象:');
  console.log('- testInvitationRoleLogic(): 测试多角色用户在evaluation邀请场景下的角色判断');
}
