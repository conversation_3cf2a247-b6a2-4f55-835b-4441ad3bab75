# FYFC 评价系统附件管理完整文档

## 📋 **文档概述**

本文档整合了FYFC评价系统附件管理功能的完整实现，包括前端组件、后端API、数据结构和使用指南。

**最后更新**: 2024年12月  
**版本**: 1.0  
**状态**: 生产就绪

## 🎯 **功能概述**

### **核心功能**
- ✅ 文件上传（单个/批量）
- ✅ 文件下载
- ✅ 文件预览
- ✅ 文件删除
- ✅ 权限控制
- ✅ 中文字符支持
- ✅ 多种文件格式支持

### **技术特性**
- ✅ OSS云存储集成
- ✅ 自动签名处理
- ✅ 响应式UI设计
- ✅ 错误处理和用户反馈
- ✅ 数据一致性保证

## 🏗️ **系统架构**

### **前端架构**
```
EvaluationEditorNew.vue
├── AttachmentSectionFixed.vue (附件管理组件)
├── BasicInfoSection.vue
├── ScoringSection.vue
└── ActionSection.vue
```

### **后端架构**
```
OSS API (/fyschedule/api/fyfc/oss)
├── /upload (文件上传)
├── /upload/batch (批量上传)
├── /attachments/{id} (获取附件列表)
├── /delete (删除文件)
├── /url (获取下载链接)
└── /preview (文件预览)
```

### **数据流程**
```
用户操作 → 前端组件 → OSS服务 → 后端API → 阿里云OSS → 数据库更新
```

## 📊 **数据结构**

### **附件数据结构**
```typescript
interface FyfcAttachment {
  id: string;             // 附件唯一ID
  fileName: string;       // 原始文件名
  fileKey: string;        // OSS文件键
  bucketName?: string;    // 存储bucket名称
  fileSize: number;       // 文件大小（字节）
  fileType: string;       // MIME类型
  uploadTime: number;     // 上传时间戳
  uploadBy: string;       // 上传人
  fileUrl?: string;       // 下载URL（临时）
}
```

### **评价实体集成**
```typescript
interface FyfcEvaluation {
  id: number;
  name: string;
  department: string;
  // ... 其他字段
  attachments?: string;   // 附件信息（JSON字符串）
}
```

### **存储格式**
评价记录的 `attachments` 字段存储格式：
```json
[
  {
    "id": "uuid-string",
    "fileName": "document.docx",
    "fileKey": "fyfc/evaluation/6/2025/06/06/filename.docx",
    "bucketName": "fyfc",
    "fileSize": 16316,
    "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "uploadTime": 1749188994768,
    "uploadBy": "用户名"
  }
]
```

## 🔧 **核心组件**

### **AttachmentSectionFixed.vue**

#### **主要特性**
- ✅ 拖拽上传支持
- ✅ 多文件上传
- ✅ 实时权限控制
- ✅ 文件类型图标
- ✅ 文件大小格式化
- ✅ 上传进度显示

#### **权限控制**
```typescript
const canUpload = computed(() => {
  if (props.readonly) return false;
  if (props.evaluationStatus === 'completed') return false;
  return props.currentUser === props.evaluationCreatedBy;
});
```

#### **使用方式**
```vue
<AttachmentSectionFixed
  :evaluation-id="formData.id"
  :upload-by="getCurrentDisplayName()"
  :evaluation-status="formData.status"
  :evaluation-created-by="formData.createdBy"
  :current-user="getCurrentDisplayName()"
  :initial-attachments="formData.attachments"
  :readonly="!canEdit"
  @attachments-updated="handleAttachmentsUpdated"
/>
```

### **FyfcOssService.ts**

#### **核心服务类**
提供完整的OSS操作封装：

```typescript
class FyfcOssService {
  // 文件上传
  async uploadFile(file: File, evaluationId: number, uploadBy: string): Promise<FyfcAttachment | null>
  
  // 批量上传
  async uploadFiles(files: File[], evaluationId: number, uploadBy: string): Promise<FyfcAttachment[]>
  
  // 获取附件列表
  async getEvaluationAttachments(evaluationId: number): Promise<FyfcAttachment[]>
  
  // 删除文件
  async deleteFile(fileKey: string, evaluationId: number, operatorName: string): Promise<boolean>
  
  // 下载文件
  async downloadFile(attachment: FyfcAttachment): Promise<void>
  
  // 预览文件
  previewFile(fileKey: string, bucketName?: string): void
}
```

#### **中文字符处理**
```typescript
// 智能编码处理
const hasChineseChars = /[\u4e00-\u9fa5]/.test(uploadBy);
if (hasChineseChars) {
  // 中文字符使用Base64编码，避免OSS签名错误
  const encoder = new TextEncoder();
  const data = encoder.encode(uploadBy);
  finalUploadBy = btoa(String.fromCharCode(...data));
}
```

## 🔗 **API接口**

### **基础信息**
- **服务地址**: `/fyschedule/api/fyfc/oss`
- **认证方式**: 无需认证（开发环境）
- **响应格式**: JSON
- **默认Bucket**: `fyfc`

### **核心接口**

#### **1. 文件上传**
```http
POST /fyschedule/api/fyfc/oss/upload
Content-Type: multipart/form-data

Parameters:
- file: File (必需)
- evaluationId: number (必需)
- uploadBy: string (必需)
- bucketName: string (可选，默认fyfc)
```

#### **2. 获取附件列表**
```http
GET /fyschedule/api/fyfc/oss/attachments/{evaluationId}
```

#### **3. 删除文件**
```http
DELETE /fyschedule/api/fyfc/oss/delete?fileKey={key}&evaluationId={id}&operatorName={name}&bucketName={bucket}
```

#### **4. 获取下载链接**
```http
GET /fyschedule/api/fyfc/oss/url?fileKey={key}&expireSeconds={seconds}&bucketName={bucket}
```

#### **5. 文件预览**
```http
GET /fyschedule/api/fyfc/oss/preview?fileKey={key}&bucketName={bucket}
```

## 🔒 **权限控制**

### **权限矩阵**

| 操作 | 评价创建人 | 其他用户 | 已完成评价 |
|------|------------|----------|------------|
| 查看附件 | ✅ | ✅ | ✅ |
| 下载附件 | ✅ | ✅ | ✅ |
| 预览附件 | ✅ | ✅ | ✅ |
| 上传附件 | ✅ | ❌ | ❌ |
| 删除附件 | ✅ | ❌ | ❌ |

### **权限实现**
```typescript
// 组件级权限控制
const canUpload = computed(() => {
  if (props.readonly) return false;
  if (props.evaluationStatus === 'completed') return false;
  return props.currentUser === props.evaluationCreatedBy;
});

// 界面权限提示
<div v-if="!canUpload && evaluationId" class="no-permission-info">
  <n-alert type="info">
    {{ evaluationStatus === 'completed' ? 
        '评价已完成，无法上传附件' : 
        '只有评价创建人可以上传附件' }}
  </n-alert>
</div>
```

## 📱 **用户界面**

### **上传区域**
- 拖拽上传支持
- 文件大小限制（50MB）
- 文件类型检查
- 上传进度显示
- 批量上传支持

### **附件列表**
- 文件类型图标
- 文件信息显示（名称、大小、上传者、时间）
- 操作按钮（下载、预览、删除）
- 权限状态提示

### **错误处理**
- 网络错误提示
- 权限错误提示
- 文件格式错误提示
- OSS签名错误处理

## 🚀 **部署和配置**

### **前端配置**
```typescript
// vite.config.ts 代理配置
server: {
  proxy: {
    '/fyschedule': {
      target: 'http://localhost:7001',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/fyschedule/, '/fyschedule2')
    }
  }
}
```

### **环境要求**
- Vue 3.x
- Naive UI
- TypeScript
- 现代浏览器支持

## 🔍 **故障排除**

### **常见问题**

#### **1. 上传失败**
- 检查文件大小是否超过50MB
- 检查网络连接
- 检查用户权限
- 查看控制台错误日志

#### **2. 中文字符问题**
- 系统自动处理中文字符编码
- 如遇问题，检查Base64编码是否正确
- 确认后端支持编码解析

#### **3. 权限问题**
- 确认当前用户是评价创建人
- 检查评价状态是否为已完成
- 验证readonly属性设置

### **调试工具**
- 浏览器开发者工具 → Network 标签
- 控制台日志输出
- OSS测试页面：`src/fyfc-oss-test.html`

## 📈 **性能优化**

### **已实现优化**
- ✅ 文件大小限制
- ✅ 批量上传支持
- ✅ 临时URL缓存
- ✅ 错误重试机制
- ✅ 响应式加载

### **建议优化**
- 文件压缩
- 缩略图生成
- 分片上传（大文件）
- CDN加速

## 📝 **更新日志**

### **v1.0 (2024年12月)**
- ✅ 完整的附件管理功能
- ✅ OSS云存储集成
- ✅ 权限控制系统
- ✅ 中文字符支持
- ✅ 错误处理机制
- ✅ 用户界面优化

## 🧪 **测试指南**

### **功能测试**

#### **上传测试**
1. 选择不同类型文件（图片、文档、PDF等）
2. 测试单个文件上传
3. 测试批量文件上传
4. 测试大文件上传（接近50MB限制）
5. 测试中文文件名
6. 测试中文用户名

#### **权限测试**
1. 评价创建人上传/删除权限
2. 非创建人权限限制
3. 已完成评价的只读状态
4. readonly模式测试

#### **错误处理测试**
1. 网络断开情况
2. 文件过大情况
3. 不支持的文件类型
4. OSS服务不可用情况

### **测试工具**
- **OSS测试页面**: `src/fyfc-oss-test.html`
- **浏览器开发者工具**: Network、Console标签
- **Postman**: API接口测试

## 📚 **开发指南**

### **添加新功能**

#### **扩展文件类型支持**
```typescript
// 在 FyfcOssService.ts 中添加
isNewFileType(fileType: string): boolean {
  return fileType.includes('new-type');
}

getFileTypeIcon(fileType: string): string {
  if (this.isNewFileType(fileType)) return '🆕';
  // ... 现有逻辑
}
```

#### **添加新的权限规则**
```typescript
// 在 AttachmentSectionFixed.vue 中修改
const canUpload = computed(() => {
  if (props.readonly) return false;
  if (props.evaluationStatus === 'completed') return false;

  // 添加新的权限逻辑
  if (props.customPermission) return props.customPermission;

  return props.currentUser === props.evaluationCreatedBy;
});
```

### **集成到新页面**

#### **1. 导入组件**
```vue
<script setup lang="ts">
import AttachmentSectionFixed from '@/components/fyfc/review/sections/AttachmentSectionFixed.vue';
</script>
```

#### **2. 添加到模板**
```vue
<template>
  <AttachmentSectionFixed
    :evaluation-id="evaluationId"
    :upload-by="currentUser"
    :evaluation-status="status"
    :evaluation-created-by="createdBy"
    :current-user="currentUser"
    :initial-attachments="attachments"
    @attachments-updated="handleAttachmentsUpdated"
  />
</template>
```

#### **3. 处理事件**
```typescript
const handleAttachmentsUpdated = (attachments: FyfcAttachment[]) => {
  // 更新本地数据
  formData.value.attachments = JSON.stringify(attachments);

  // 触发保存或其他逻辑
  console.log('附件已更新:', attachments.length, '个文件');
};
```

## 🔧 **配置选项**

### **组件配置**
```typescript
interface AttachmentSectionProps {
  evaluationId?: number;        // 评价ID
  uploadBy: string;            // 上传人
  evaluationStatus?: string;   // 评价状态
  evaluationCreatedBy?: string; // 评价创建人
  currentUser: string;         // 当前用户
  readonly?: boolean;          // 只读模式
  initialAttachments?: string; // 初始附件数据
  maxFileSize?: number;        // 最大文件大小（默认50MB）
  maxFileCount?: number;       // 最大文件数量（默认10个）
  allowedTypes?: string[];     // 允许的文件类型
}
```

### **服务配置**
```typescript
class FyfcOssService {
  private readonly baseUrl = '/fyschedule/api/fyfc/oss';
  private readonly defaultBucket = 'fyfc';
  private readonly defaultExpireSeconds = 3600;

  // 可配置的选项
  configure(options: {
    baseUrl?: string;
    defaultBucket?: string;
    defaultExpireSeconds?: number;
  }) {
    // 配置逻辑
  }
}
```

## 📋 **最佳实践**

### **性能优化**
1. **文件大小控制**: 限制单个文件50MB以内
2. **批量上传**: 一次最多10个文件
3. **缓存策略**: 下载URL缓存1小时
4. **错误重试**: 网络错误自动重试3次

### **用户体验**
1. **进度反馈**: 上传过程显示进度条
2. **错误提示**: 清晰的错误信息和解决建议
3. **权限提示**: 明确的权限状态说明
4. **响应式设计**: 适配不同屏幕尺寸

### **安全考虑**
1. **文件类型检查**: 前后端双重验证
2. **文件大小限制**: 防止恶意上传
3. **权限控制**: 严格的用户权限验证
4. **签名安全**: OSS签名防篡改

### **代码规范**
1. **TypeScript**: 严格的类型定义
2. **错误处理**: 完整的try-catch覆盖
3. **日志记录**: 详细的调试信息
4. **代码注释**: 清晰的功能说明

## 🔄 **版本兼容性**

### **当前版本**: v1.0
- Vue 3.x
- Naive UI 2.x
- TypeScript 4.x+

### **升级指南**
从旧版本升级时需要注意：
1. 检查API接口变更
2. 更新组件属性
3. 验证权限逻辑
4. 测试文件上传功能

## 📞 **技术支持**

### **常见问题解答**
- **Q**: 为什么上传失败？
- **A**: 检查文件大小、网络连接和用户权限

- **Q**: 中文文件名显示乱码？
- **A**: 系统已自动处理，如有问题请联系技术支持

- **Q**: 如何扩展支持的文件类型？
- **A**: 参考开发指南中的扩展方法

### **联系方式**
- **开发团队**: FYFC开发组
- **技术支持**: 请通过内部系统提交工单
- **文档维护**: 定期更新，版本控制

---

**维护团队**: FYFC开发组
**技术支持**: 请联系开发团队
**文档版本**: 1.0
**最后更新**: 2024年12月
