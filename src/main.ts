import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router';
import naive from 'naive-ui';
import { createDiscreteApi } from 'naive-ui';
import { initUserContext } from './utils/UserContext';

const app = createApp(App);
// 使用 Naive UI 插件
app.use(naive);

// 创建离散 API 并提供给全局
const { message } = createDiscreteApi(['message']);
app.provide('message', message);
app.use(router);

// 在应用挂载前初始化用户上下文，确保用户信息在页面加载前就准备好
async function startApp() {
  // 等待路由准备完成
  await router.isReady();

  // 初始化用户上下文
  await initUserContext();

  // 挂载应用
  app.mount('#app');
}

// 启动应用
startApp().catch(error => {
  console.error('应用启动失败:', error);
  // 即使初始化失败也要挂载应用，避免白屏
  app.mount('#app');
});
