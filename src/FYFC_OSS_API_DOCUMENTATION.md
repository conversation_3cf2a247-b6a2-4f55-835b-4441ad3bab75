# FYFC 评价系统 OSS 接口文档

## 📋 **接口概述**

FYFC 评价系统提供完整的文件上传、下载、删除和管理功能，支持多种文件类型和多 bucket 存储。

### **基础信息**

- **服务地址**: `https://localhost:7001/fyschedule`
- **接口前缀**: `/api/fyfc/oss`
- **认证方式**: 无需认证（开发环境）
- **响应格式**: JSON

### **通用响应格式**

```typescript
interface FyfcApiResponse<T> {
  success: boolean;        // 操作是否成功
  code?: number;          // 错误码（失败时）
  message: string;        // 响应消息
  data: T | null;         // 响应数据
  timestamp: number;      // 时间戳
}
```

### **数据结构定义**

#### **附件数据结构**

```typescript
interface FyfcAttachment {
  id: string;             // 附件唯一ID
  fileName: string;       // 原始文件名
  fileKey: string;        // OSS文件键（用于下载/删除）
  bucketName?: string;    // 存储的bucket名称
  fileSize: number;       // 文件大小（字节）
  fileType: string;       // MIME类型
  uploadTime: number;     // 上传时间戳
  uploadBy: string;       // 上传人
  fileUrl?: string;       // 下载URL（临时，有过期时间）
}
```

#### **评价实体数据结构**

```typescript
interface FyfcEvaluation {
  id: number;                    // 评价ID
  name: string;                  // 被评价人姓名
  department: string;            // 部门
  position: string;              // 职位
  status: string;                // 评价状态
  score?: number;                // 评价分数
  selfScore?: number;            // 自评分数
  colleagueScore?: number;       // 同事评分
  managerScore?: number;         // 经理评分
  attachments?: string;          // 附件信息（JSON字符串）
  createdBy: string;             // 创建人
  updatedBy?: string;            // 更新人
  createdTime: number;           // 创建时间
  updatedTime?: number;          // 更新时间
  // ... 其他字段
}
```

#### **附件存储格式**

评价实体的 `attachments` 字段存储格式为 JSON 字符串：

```json
[
  {
    "id": "8eb3b178-23bd-4533-ab81-9d51db239978",
    "fileName": "Samsung Galaxy S25 Ultra.docx",
    "fileKey": "fyfc/evaluation/6/2025/06/06/09e5d5eacf4d4fe5b42353baa4b57dd4.docx",
    "bucketName": "fyfc",
    "fileSize": 16316,
    "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "uploadTime": 1749188994768,
    "uploadBy": "testUser"
  }
]
```

## 🔗 **附件与评价的关系**

### **数据关联机制**

FYFC 评价系统中，附件与评价记录通过以下方式关联：

1. **存储位置**：附件信息存储在 `fyfc_evaluations` 表的 `attachments` 字段中
2. **存储格式**：JSON 数组字符串，包含所有附件的完整信息
3. **自动管理**：上传、删除操作会自动更新评价记录中的附件信息

### **数据流程**

```mermaid
graph TD
    A[用户上传文件] --> B[文件存储到OSS]
    B --> C[生成附件信息]
    C --> D[更新evaluation.attachments字段]

    E[用户删除文件] --> F[从OSS删除文件]
    F --> G[从evaluation.attachments中移除]

    H[获取附件列表] --> I[从evaluation.attachments读取]
    I --> J[为每个附件生成下载URL]
    J --> K[返回完整附件信息]
```

### **数据库操作**

#### **查看评价的附件信息**

```sql
-- 查看评价记录的附件
SELECT id, name, attachments
FROM fyfc_evaluations
WHERE id = 6;

-- 解析附件JSON信息
SELECT
    id,
    name,
    JSON_LENGTH(attachments) as attachment_count,
    JSON_EXTRACT(attachments, '$[0].fileName') as first_file_name,
    JSON_EXTRACT(attachments, '$[0].bucketName') as first_file_bucket
FROM fyfc_evaluations
WHERE attachments IS NOT NULL
  AND attachments != '[]';
```

#### **附件统计查询**

```sql
-- 统计各评价的附件数量
SELECT
    id,
    name,
    CASE
        WHEN attachments IS NULL OR attachments = '[]' THEN 0
        ELSE JSON_LENGTH(attachments)
    END as attachment_count
FROM fyfc_evaluations
ORDER BY attachment_count DESC;
```

### **前端数据处理**

#### **从评价数据中提取附件**

```typescript
function extractAttachmentsFromEvaluation(evaluation: FyfcEvaluation): FyfcAttachment[] {
  if (!evaluation.attachments) {
    return [];
  }

  try {
    return JSON.parse(evaluation.attachments) as FyfcAttachment[];
  } catch (error) {
    console.error('解析附件数据失败:', error);
    return [];
  }
}
```

#### **更新评价的附件信息**

```typescript
function updateEvaluationAttachments(
  evaluation: FyfcEvaluation,
  attachments: FyfcAttachment[]
): FyfcEvaluation {
  return {
    ...evaluation,
    attachments: JSON.stringify(attachments)
  };
}
```

### **注意事项**

1. **数据一致性**：附件操作会自动同步到评价记录，无需手动更新
2. **JSON 格式**：attachments 字段存储的是 JSON 字符串，不是对象
3. **空值处理**：无附件时，字段值为 `null` 或空数组 `"[]"`
4. **并发安全**：附件操作使用事务确保数据一致性

## 🚀 **核心接口**

### **1. 上传单个文件**

```http
POST /api/fyfc/oss/upload
Content-Type: multipart/form-data
```

**请求参数**：
```typescript
interface UploadFileRequest {
  file: File;                    // 文件对象（必需）
  evaluationId: number;          // 评价ID（必需）
  uploadBy: string;              // 上传人（必需）
  bucketName?: string;           // bucket名称（可选）
}
```

**响应示例**：
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "id": "8eb3b178-23bd-4533-ab81-9d51db239978",
    "fileName": "document.docx",
    "fileKey": "fyfc/evaluation/6/2025/06/06/09e5d5eacf4d4fe5b42353baa4b57dd4.docx",
    "bucketName": "fyfc",
    "fileSize": 16316,
    "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "uploadTime": 1749188994768,
    "uploadBy": "testUser"
  },
  "timestamp": 1749188994768
}
```

**前端使用示例**：
```typescript
async function uploadFile(file: File, evaluationId: number, uploadBy: string, bucketName?: string): Promise<FyfcAttachment | null> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('evaluationId', evaluationId.toString());
  formData.append('uploadBy', uploadBy);
  
  if (bucketName) {
    formData.append('bucketName', bucketName);
  }
  
  try {
    const response = await fetch('/fyschedule2/api/fyfc/oss/upload', {
      method: 'POST',
      body: formData
    });
    
    const result: FyfcApiResponse<FyfcAttachment> = await response.json();
    
    if (result.success) {
      return result.data;
    } else {
      console.error('上传失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('上传异常:', error);
    return null;
  }
}
```

### **2. 批量上传文件**

```http
POST /api/fyfc/oss/upload/batch
Content-Type: multipart/form-data
```

**请求参数**：
```typescript
interface BatchUploadRequest {
  files: File[];                 // 文件数组（必需）
  evaluationId: number;          // 评价ID（必需）
  uploadBy: string;              // 上传人（必需）
  bucketName?: string;           // bucket名称（可选）
}
```

**响应示例**：
```json
{
  "success": true,
  "message": "批量上传完成",
  "data": [
    {
      "id": "uuid-1",
      "fileName": "file1.pdf",
      "fileKey": "fyfc/evaluation/6/2025/06/06/file1.pdf",
      "bucketName": "fyfc",
      "fileSize": 12345,
      "fileType": "application/pdf",
      "uploadTime": 1749188994768,
      "uploadBy": "testUser"
    },
    {
      "id": "uuid-2",
      "fileName": "file2.jpg",
      "fileKey": "fyfc/evaluation/6/2025/06/06/file2.jpg",
      "bucketName": "fyfc",
      "fileSize": 67890,
      "fileType": "image/jpeg",
      "uploadTime": 1749188994769,
      "uploadBy": "testUser"
    }
  ],
  "timestamp": 1749188994768
}
```

**前端使用示例**：
```typescript
async function uploadFiles(files: File[], evaluationId: number, uploadBy: string, bucketName?: string): Promise<FyfcAttachment[]> {
  const formData = new FormData();
  
  // 添加多个文件
  files.forEach(file => {
    formData.append('files', file);
  });
  
  formData.append('evaluationId', evaluationId.toString());
  formData.append('uploadBy', uploadBy);
  
  if (bucketName) {
    formData.append('bucketName', bucketName);
  }
  
  try {
    const response = await fetch('/fyschedule2/api/fyfc/oss/upload/batch', {
      method: 'POST',
      body: formData
    });
    
    const result: FyfcApiResponse<FyfcAttachment[]> = await response.json();
    
    if (result.success) {
      return result.data || [];
    } else {
      console.error('批量上传失败:', result.message);
      return [];
    }
  } catch (error) {
    console.error('批量上传异常:', error);
    return [];
  }
}
```

### **3. 获取评价附件列表**

```http
GET /api/fyfc/oss/attachments/{evaluationId}
```

**路径参数**：
- `evaluationId`: 评价ID

**响应示例**：
```json
{
  "success": true,
  "message": "获取附件列表成功",
  "data": [
    {
      "id": "8eb3b178-23bd-4533-ab81-9d51db239978",
      "fileName": "document.docx",
      "fileKey": "fyfc/evaluation/6/2025/06/06/09e5d5eacf4d4fe5b42353baa4b57dd4.docx",
      "bucketName": "fyfc",
      "fileSize": 16316,
      "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "uploadTime": 1749188994768,
      "uploadBy": "testUser",
      "fileUrl": "https://fyfc.oss-cn-shanghai.aliyuncs.com/fyfc/evaluation/6/2025/06/06/09e5d5eacf4d4fe5b42353baa4b57dd4.docx?Expires=1749192594&OSSAccessKeyId=..."
    }
  ],
  "timestamp": 1749188994768
}
```

**前端使用示例**：
```typescript
async function getEvaluationAttachments(evaluationId: number): Promise<FyfcAttachment[]> {
  try {
    const response = await fetch(`/fyschedule2/api/fyfc/oss/attachments/${evaluationId}`);
    const result: FyfcApiResponse<FyfcAttachment[]> = await response.json();
    
    if (result.success) {
      return result.data || [];
    } else {
      console.error('获取附件列表失败:', result.message);
      return [];
    }
  } catch (error) {
    console.error('获取附件列表异常:', error);
    return [];
  }
}
```

### **4. 删除文件**

```http
DELETE /api/fyfc/oss/delete
```

**查询参数**：
```typescript
interface DeleteFileRequest {
  fileKey: string;               // 文件键（必需）
  evaluationId: number;          // 评价ID（必需）
  operatorName: string;          // 操作人（必需）
  bucketName?: string;           // bucket名称（可选）
}
```

**响应示例**：
```json
{
  "success": true,
  "message": "文件删除成功",
  "data": true,
  "timestamp": 1749188994768
}
```

**前端使用示例**：
```typescript
async function deleteFile(fileKey: string, evaluationId: number, operatorName: string, bucketName?: string): Promise<boolean> {
  const params = new URLSearchParams({
    fileKey: fileKey,
    evaluationId: evaluationId.toString(),
    operatorName: operatorName
  });
  
  if (bucketName) {
    params.append('bucketName', bucketName);
  }
  
  try {
    const response = await fetch(`/fyschedule2/api/fyfc/oss/delete?${params}`, {
      method: 'DELETE'
    });
    
    const result: FyfcApiResponse<boolean> = await response.json();
    
    if (result.success) {
      return result.data || false;
    } else {
      console.error('删除文件失败:', result.message);
      return false;
    }
  } catch (error) {
    console.error('删除文件异常:', error);
    return false;
  }
}
```

### **5. 获取文件下载URL**

```http
GET /api/fyfc/oss/url
```

**查询参数**：
```typescript
interface GetFileUrlRequest {
  fileKey: string;               // 文件键（必需）
  expireSeconds?: number;        // 过期时间秒数（可选，默认3600）
  bucketName?: string;           // bucket名称（可选）
}
```

**响应示例**：
```json
{
  "success": true,
  "message": "获取文件URL成功",
  "data": "https://fyfc.oss-cn-shanghai.aliyuncs.com/fyfc/evaluation/6/2025/06/06/09e5d5eacf4d4fe5b42353baa4b57dd4.docx?Expires=1749192594&OSSAccessKeyId=...",
  "timestamp": 1749188994768
}
```

**前端使用示例**：
```typescript
async function getFileDownloadUrl(fileKey: string, expireSeconds: number = 3600, bucketName?: string): Promise<string | null> {
  const params = new URLSearchParams({
    fileKey: fileKey,
    expireSeconds: expireSeconds.toString()
  });
  
  if (bucketName) {
    params.append('bucketName', bucketName);
  }
  
  try {
    const response = await fetch(`/fyschedule2/api/fyfc/oss/url?${params}`);
    const result: FyfcApiResponse<string> = await response.json();
    
    if (result.success) {
      return result.data;
    } else {
      console.error('获取下载URL失败:', result.message);
      return null;
    }
  } catch (error) {
    console.error('获取下载URL异常:', error);
    return null;
  }
}
```

### **6. 文件预览**

```http
GET /api/fyfc/oss/preview
```

**查询参数**：
```typescript
interface PreviewFileRequest {
  fileKey: string;               // 文件键（必需）
  expireSeconds?: number;        // 过期时间秒数（可选，默认3600）
  bucketName?: string;           // bucket名称（可选）
}
```

**响应**: 重定向到文件URL或返回错误页面

**前端使用示例**：
```typescript
function previewFile(fileKey: string, bucketName?: string): void {
  const params = new URLSearchParams({
    fileKey: fileKey,
    expireSeconds: '3600'
  });
  
  if (bucketName) {
    params.append('bucketName', bucketName);
  }
  
  // 在新窗口打开预览
  window.open(`/fyschedule2/api/fyfc/oss/preview?${params}`, '_blank');
}
```

## 🎯 **完整的前端工具类**

```typescript
/**
 * FYFC OSS 服务工具类
 */
export class FyfcOssService {
  private readonly baseUrl = '/fyschedule2/api/fyfc/oss';
  
  /**
   * 上传单个文件
   */
  async uploadFile(
    file: File, 
    evaluationId: number, 
    uploadBy: string, 
    bucketName?: string
  ): Promise<FyfcAttachment | null> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('evaluationId', evaluationId.toString());
    formData.append('uploadBy', uploadBy);
    
    if (bucketName) {
      formData.append('bucketName', bucketName);
    }
    
    try {
      const response = await fetch(`${this.baseUrl}/upload`, {
        method: 'POST',
        body: formData
      });
      
      const result: FyfcApiResponse<FyfcAttachment> = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('上传文件失败:', error);
      return null;
    }
  }
  
  /**
   * 批量上传文件
   */
  async uploadFiles(
    files: File[], 
    evaluationId: number, 
    uploadBy: string, 
    bucketName?: string
  ): Promise<FyfcAttachment[]> {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });
    
    formData.append('evaluationId', evaluationId.toString());
    formData.append('uploadBy', uploadBy);
    
    if (bucketName) {
      formData.append('bucketName', bucketName);
    }
    
    try {
      const response = await fetch(`${this.baseUrl}/upload/batch`, {
        method: 'POST',
        body: formData
      });
      
      const result: FyfcApiResponse<FyfcAttachment[]> = await response.json();
      return result.success ? (result.data || []) : [];
    } catch (error) {
      console.error('批量上传文件失败:', error);
      return [];
    }
  }
  
  /**
   * 获取评价附件列表
   */
  async getEvaluationAttachments(evaluationId: number): Promise<FyfcAttachment[]> {
    try {
      const response = await fetch(`${this.baseUrl}/attachments/${evaluationId}`);
      const result: FyfcApiResponse<FyfcAttachment[]> = await response.json();
      return result.success ? (result.data || []) : [];
    } catch (error) {
      console.error('获取附件列表失败:', error);
      return [];
    }
  }
  
  /**
   * 删除文件
   */
  async deleteFile(
    fileKey: string, 
    evaluationId: number, 
    operatorName: string, 
    bucketName?: string
  ): Promise<boolean> {
    const params = new URLSearchParams({
      fileKey: fileKey,
      evaluationId: evaluationId.toString(),
      operatorName: operatorName
    });
    
    if (bucketName) {
      params.append('bucketName', bucketName);
    }
    
    try {
      const response = await fetch(`${this.baseUrl}/delete?${params}`, {
        method: 'DELETE'
      });
      
      const result: FyfcApiResponse<boolean> = await response.json();
      return result.success && (result.data || false);
    } catch (error) {
      console.error('删除文件失败:', error);
      return false;
    }
  }
  
  /**
   * 获取文件下载URL
   */
  async getFileDownloadUrl(
    fileKey: string, 
    expireSeconds: number = 3600, 
    bucketName?: string
  ): Promise<string | null> {
    const params = new URLSearchParams({
      fileKey: fileKey,
      expireSeconds: expireSeconds.toString()
    });
    
    if (bucketName) {
      params.append('bucketName', bucketName);
    }
    
    try {
      const response = await fetch(`${this.baseUrl}/url?${params}`);
      const result: FyfcApiResponse<string> = await response.json();
      return result.success ? result.data : null;
    } catch (error) {
      console.error('获取下载URL失败:', error);
      return null;
    }
  }
  
  /**
   * 下载文件
   */
  async downloadFile(attachment: FyfcAttachment): Promise<void> {
    try {
      const url = await this.getFileDownloadUrl(
        attachment.fileKey, 
        3600, 
        attachment.bucketName
      );
      
      if (url) {
        const link = document.createElement('a');
        link.href = url;
        link.download = attachment.fileName;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        throw new Error('无法获取下载链接');
      }
    } catch (error) {
      console.error('下载文件失败:', error);
      throw error;
    }
  }
  
  /**
   * 预览文件
   */
  previewFile(fileKey: string, bucketName?: string): void {
    const params = new URLSearchParams({
      fileKey: fileKey,
      expireSeconds: '3600'
    });
    
    if (bucketName) {
      params.append('bucketName', bucketName);
    }
    
    window.open(`${this.baseUrl}/preview?${params}`, '_blank');
  }
  
  /**
   * 格式化文件大小
   */
  formatFileSize(sizeInBytes: number): string {
    if (sizeInBytes < 1024) {
      return `${sizeInBytes} B`;
    } else if (sizeInBytes < 1024 * 1024) {
      return `${(sizeInBytes / 1024).toFixed(1)} KB`;
    } else if (sizeInBytes < 1024 * 1024 * 1024) {
      return `${(sizeInBytes / (1024 * 1024)).toFixed(1)} MB`;
    } else {
      return `${(sizeInBytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    }
  }
  
  /**
   * 检查文件类型是否为图片
   */
  isImageFile(fileType: string): boolean {
    return fileType.startsWith('image/');
  }
  
  /**
   * 检查文件类型是否为文档
   */
  isDocumentFile(fileType: string): boolean {
    const documentTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation'
    ];
    return documentTypes.includes(fileType);
  }

  /**
   * 从评价数据中提取附件列表
   */
  extractAttachmentsFromEvaluation(evaluation: FyfcEvaluation): FyfcAttachment[] {
    if (!evaluation.attachments) {
      return [];
    }

    try {
      return JSON.parse(evaluation.attachments) as FyfcAttachment[];
    } catch (error) {
      console.error('解析附件数据失败:', error);
      return [];
    }
  }

  /**
   * 更新评价的附件信息
   */
  updateEvaluationAttachments(
    evaluation: FyfcEvaluation,
    attachments: FyfcAttachment[]
  ): FyfcEvaluation {
    return {
      ...evaluation,
      attachments: JSON.stringify(attachments)
    };
  }

  /**
   * 统计评价的附件数量
   */
  getAttachmentCount(evaluation: FyfcEvaluation): number {
    const attachments = this.extractAttachmentsFromEvaluation(evaluation);
    return attachments.length;
  }

  /**
   * 计算评价附件的总大小
   */
  getTotalAttachmentSize(evaluation: FyfcEvaluation): number {
    const attachments = this.extractAttachmentsFromEvaluation(evaluation);
    return attachments.reduce((total, attachment) => total + attachment.fileSize, 0);
  }
}

// 创建单例实例
export const fyfcOssService = new FyfcOssService();
```

## 🎨 **React 组件示例**

```tsx
import React, { useState, useEffect } from 'react';
import { fyfcOssService, FyfcAttachment } from './FyfcOssService';

interface AttachmentManagerProps {
  evaluationId: number;
  uploadBy: string;
  readonly?: boolean;
}

export const AttachmentManager: React.FC<AttachmentManagerProps> = ({
  evaluationId,
  uploadBy,
  readonly = false
}) => {
  const [attachments, setAttachments] = useState<FyfcAttachment[]>([]);
  const [uploading, setUploading] = useState(false);
  const [loading, setLoading] = useState(false);

  // 加载附件列表
  const loadAttachments = async () => {
    setLoading(true);
    try {
      const data = await fyfcOssService.getEvaluationAttachments(evaluationId);
      setAttachments(data);
    } catch (error) {
      console.error('加载附件失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 上传文件
  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return;

    setUploading(true);
    try {
      const fileArray = Array.from(files);
      const uploadedAttachments = await fyfcOssService.uploadFiles(
        fileArray,
        evaluationId,
        uploadBy
      );
      
      if (uploadedAttachments.length > 0) {
        await loadAttachments(); // 重新加载列表
      }
    } catch (error) {
      console.error('上传失败:', error);
    } finally {
      setUploading(false);
    }
  };

  // 删除文件
  const handleDelete = async (attachment: FyfcAttachment) => {
    if (!confirm(`确定要删除文件 "${attachment.fileName}" 吗？`)) {
      return;
    }

    try {
      const success = await fyfcOssService.deleteFile(
        attachment.fileKey,
        evaluationId,
        uploadBy,
        attachment.bucketName
      );
      
      if (success) {
        await loadAttachments(); // 重新加载列表
      }
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  // 下载文件
  const handleDownload = async (attachment: FyfcAttachment) => {
    try {
      await fyfcOssService.downloadFile(attachment);
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请稍后重试');
    }
  };

  // 预览文件
  const handlePreview = (attachment: FyfcAttachment) => {
    fyfcOssService.previewFile(attachment.fileKey, attachment.bucketName);
  };

  useEffect(() => {
    loadAttachments();
  }, [evaluationId]);

  return (
    <div className="attachment-manager">
      <div className="attachment-header">
        <h3>附件管理</h3>
        {!readonly && (
          <div className="upload-area">
            <input
              type="file"
              multiple
              onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
              disabled={uploading}
            />
            {uploading && <span>上传中...</span>}
          </div>
        )}
      </div>

      <div className="attachment-list">
        {loading ? (
          <div>加载中...</div>
        ) : attachments.length === 0 ? (
          <div>暂无附件</div>
        ) : (
          attachments.map((attachment) => (
            <div key={attachment.id} className="attachment-item">
              <div className="attachment-info">
                <div className="file-name">{attachment.fileName}</div>
                <div className="file-meta">
                  <span>{fyfcOssService.formatFileSize(attachment.fileSize)}</span>
                  <span>{attachment.uploadBy}</span>
                  <span>{new Date(attachment.uploadTime).toLocaleString()}</span>
                  {attachment.bucketName && (
                    <span className="bucket-info">Bucket: {attachment.bucketName}</span>
                  )}
                </div>
              </div>
              
              <div className="attachment-actions">
                <button onClick={() => handleDownload(attachment)}>
                  下载
                </button>
                
                {(fyfcOssService.isImageFile(attachment.fileType) || 
                  fyfcOssService.isDocumentFile(attachment.fileType)) && (
                  <button onClick={() => handlePreview(attachment)}>
                    预览
                  </button>
                )}
                
                {!readonly && (
                  <button 
                    onClick={() => handleDelete(attachment)}
                    className="delete-btn"
                  >
                    删除
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
```

## 🔄 **评价实体变更说明**

### **新增字段**

FYFC 评价系统的评价实体 (`FyfcEvaluation`) 新增了 `attachments` 字段：

```sql
-- 数据库表结构变更
ALTER TABLE fyfc_evaluations
ADD COLUMN attachments TEXT COMMENT '附件信息JSON';
```

### **字段说明**

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `attachments` | TEXT | 附件信息的JSON字符串 | `[{"id":"uuid","fileName":"doc.pdf",...}]` |

### **数据格式**

```typescript
// 空附件
evaluation.attachments = null;           // 或
evaluation.attachments = "[]";

// 有附件
evaluation.attachments = JSON.stringify([
  {
    "id": "8eb3b178-23bd-4533-ab81-9d51db239978",
    "fileName": "document.docx",
    "fileKey": "fyfc/evaluation/6/2025/06/06/file.docx",
    "bucketName": "fyfc",
    "fileSize": 16316,
    "fileType": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "uploadTime": 1749188994768,
    "uploadBy": "testUser"
  }
]);
```

### **API 影响**

#### **评价查询接口**

现在评价查询接口会返回附件信息：

```typescript
// GET /api/fyfc/evaluation/common/{id}
interface EvaluationResponse {
  id: number;
  name: string;
  department: string;
  // ... 其他字段
  attachments?: string;  // 新增：附件JSON字符串
}
```

#### **前端处理示例**

```typescript
// 获取评价详情时处理附件
async function getEvaluationWithAttachments(evaluationId: number) {
  // 1. 获取评价基本信息
  const evaluation = await getEvaluation(evaluationId);

  // 2. 解析附件信息
  const attachments = fyfcOssService.extractAttachmentsFromEvaluation(evaluation);

  // 3. 为附件生成下载URL（可选）
  const attachmentsWithUrls = await Promise.all(
    attachments.map(async (attachment) => {
      const url = await fyfcOssService.getFileDownloadUrl(
        attachment.fileKey,
        3600,
        attachment.bucketName
      );
      return { ...attachment, fileUrl: url };
    })
  );

  return {
    ...evaluation,
    attachmentList: attachmentsWithUrls  // 解析后的附件列表
  };
}
```

### **兼容性说明**

1. **向后兼容**：现有的评价记录 `attachments` 字段为 `null`，不影响现有功能
2. **渐进升级**：可以逐步为评价记录添加附件，无需一次性迁移
3. **数据完整性**：附件操作会自动维护 `attachments` 字段的数据一致性

### **最佳实践**

#### **1. 统一使用 OSS 接口**

```typescript
// ✅ 推荐：使用专门的 OSS 接口
const attachments = await fyfcOssService.getEvaluationAttachments(evaluationId);

// ❌ 不推荐：直接解析评价实体的 attachments 字段
const attachments = JSON.parse(evaluation.attachments || '[]');
```

#### **2. 附件数量显示**

```typescript
// 在评价列表中显示附件数量
function EvaluationListItem({ evaluation }: { evaluation: FyfcEvaluation }) {
  const attachmentCount = fyfcOssService.getAttachmentCount(evaluation);

  return (
    <div className="evaluation-item">
      <span>{evaluation.name}</span>
      {attachmentCount > 0 && (
        <span className="attachment-badge">📎 {attachmentCount}</span>
      )}
    </div>
  );
}
```

#### **3. 附件大小统计**

```typescript
// 统计评价的附件总大小
function getEvaluationAttachmentSummary(evaluation: FyfcEvaluation) {
  const count = fyfcOssService.getAttachmentCount(evaluation);
  const totalSize = fyfcOssService.getTotalAttachmentSize(evaluation);

  return {
    count,
    totalSize,
    formattedSize: fyfcOssService.formatFileSize(totalSize)
  };
}
```

## 📝 **使用注意事项**

### **1. 文件大小限制**
- 单个文件最大: **50MB**
- 批量上传总大小: **500MB**
- 单次最多文件数: **10个**

### **2. 支持的文件类型**
- 默认允许所有类型（除安全黑名单）
- 禁止的类型: `.exe`, `.bat`, `.sh`, `.js` 等可执行文件

### **3. Bucket 管理**
- 如果不指定 `bucketName`，使用默认 bucket
- 建议在上传时明确指定 bucket
- 下载时会自动使用附件存储的 bucket 信息

### **4. URL 过期时间**
- 默认下载 URL 有效期: **1小时**
- 可通过 `expireSeconds` 参数自定义
- 建议根据使用场景设置合适的过期时间

### **5. 错误处理**
- 所有接口都返回统一的响应格式
- 建议检查 `success` 字段判断操作是否成功
- 失败时查看 `message` 字段获取错误信息

## 🎉 **总结**

这份文档提供了 FYFC 评价系统 OSS 接口的完整使用指南，包括：

### **核心内容**

1. **完整的接口定义**：所有 OSS 相关接口的详细说明
2. **TypeScript 类型定义**：便于前端开发时的类型检查
3. **数据结构说明**：评价实体和附件的关联关系
4. **实用的工具类**：开箱即用的 OSS 服务封装
5. **React 组件示例**：完整的附件管理组件
6. **最佳实践指导**：使用注意事项和建议

### **重要变更**

- **评价实体新增 `attachments` 字段**：存储附件信息的 JSON 字符串
- **自动数据同步**：附件操作会自动更新评价记录
- **完整的数据关联**：附件与评价通过数据库字段关联

### **使用建议**

1. **优先使用 OSS 专用接口**：`getEvaluationAttachments()` 而不是直接解析 JSON
2. **注意 bucket 参数**：确保上传和下载使用正确的 bucket
3. **处理异步操作**：文件操作都是异步的，需要适当的加载状态
4. **错误处理**：检查接口返回的 `success` 字段

### **快速开始**

```typescript
// 1. 导入工具类
import { fyfcOssService } from './FyfcOssService';

// 2. 上传文件
const attachment = await fyfcOssService.uploadFile(file, evaluationId, 'user123');

// 3. 获取附件列表
const attachments = await fyfcOssService.getEvaluationAttachments(evaluationId);

// 4. 下载文件
await fyfcOssService.downloadFile(attachment);
```

前端开发者和 AI 助手可以直接使用提供的工具类和组件，快速集成 OSS 功能到 FYFC 评价系统中。
