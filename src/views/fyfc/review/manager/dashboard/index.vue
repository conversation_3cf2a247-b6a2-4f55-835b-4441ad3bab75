<template>
    <div class="page-wrapper">
        <div class="container">
            <div class="content">
                <n-card title="待我评价的绩效" size="small" :bordered="true" class="dashboard-card">
            <!-- 右上角注销按钮 -->
            <template #header-extra>
                <LogoutButton :showSessionInfo="false" />
            </template>

            <!-- 评价列表 -->
            <n-list v-if="sortedEvaluationList.length > 0" hoverable clickable>
                <n-list-item
                    v-for="evaluation in sortedEvaluationList"
                    :key="evaluation.id"
                    @click="handleItemClick(evaluation)"
                    class="evaluation-item"
                >
                    <template #prefix>
                        <n-avatar
                            round
                            size="medium"
                            :style="{ backgroundColor: getStatusColor(evaluation.status) }"
                        >
                            <n-icon><DocumentIcon /></n-icon>
                        </n-avatar>
                    </template>

                    <n-thing>
                        <template #header>
                            <div class="evaluation-header">
                                <span class="evaluation-title">
                                    {{ evaluation.name }} - {{ formatDate(evaluation.reviewDate) }} 绩效评价
                                </span>
                                <n-tag
                                    :type="getStatusTagType(evaluation.status)"
                                    size="small"
                                    class="status-tag"
                                >
                                    {{ getStatusLabel(evaluation.status as EvaluationStatus) }}
                                </n-tag>
                            </div>
                        </template>

                        <template #description>
                            <div class="evaluation-details">
                                <div class="detail-item">
                                    <n-icon class="detail-icon"><PersonIcon /></n-icon>
                                    <span class="detail-label">员工姓名:</span>
                                    <span class="detail-value">{{ evaluation.name || '未填写' }}</span>
                                </div>
                                <div class="detail-item">
                                    <n-icon class="detail-icon"><DepartmentIcon /></n-icon>
                                    <span class="detail-label">部门:</span>
                                    <span class="detail-value">{{ evaluation.department || '未填写' }}</span>
                                </div>

                                <div class="detail-item">
                                    <n-icon class="detail-icon"><TimeIcon /></n-icon>
                                    <span class="detail-label">创建时间:</span>
                                    <span class="detail-value">{{ formatDateTime(evaluation.createdAt) }}</span>
                                </div>
                            </div>
                        </template>

                        <template #action>
                            <n-button
                                size="small"
                                type="primary"
                                ghost
                                @click.stop="handleEvaluate(evaluation)"
                            >
                                进行评价
                            </n-button>
                        </template>
                    </n-thing>
                </n-list-item>
            </n-list>

            <!-- 空状态 -->
            <n-empty
                v-else
                description="暂无待评价记录"
                class="empty-state"
                size="large"
            >
                <template #icon>
                    <n-icon><EmptyIcon /></n-icon>
                </template>
                <template #extra>
                    <n-text depth="3">
                        当员工完成自评或同事评价后，相关记录会出现在这里
                    </n-text>
                </template>
            </n-empty>

            <!-- 加载状态 -->
            <div v-if="loading" class="loading-container">
                <n-spin size="large">
                    <template #description>
                        加载待评价列表...
                    </template>
                </n-spin>
            </div>
                </n-card>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { generateEncryptedUrl } from '../../../../../utils/crypto';
import LogoutButton from '../../../../../components/common/LogoutButton.vue';
import {
    NCard,
    NList,
    NListItem,
    NButton,
    NIcon,
    NAvatar,
    NThing,
    NTag,
    NEmpty,
    NSpin,
    NText,
    useMessage
} from 'naive-ui';
import {
    Document as DocumentIcon,
    Person as PersonIcon,
    Business as DepartmentIcon,
    Time as TimeIcon,
    FolderOpen as EmptyIcon
} from '@vicons/ionicons5';
import type { Evaluation, EvaluationStatus } from '../../../../../fconfig/fyfc/review';
import { getStatusLabel } from '../../../../../fconfig/fyfc/review';
import { fyfcReviewApi, type EvaluationQueryDto } from '../../../../../utils/FyfcReviewApi';
import { useUserContext } from '../../../../../utils/UserContext';

// 路由和消息
const router = useRouter();
const message = useMessage();

// 获取当前用户信息
// const vueCookies: any = VueCookies;
// const currentUser = vueCookies.get('account');
const { getCurrentDisplayName } = useUserContext();
const currentUser = getCurrentDisplayName();

// 响应式数据
const loading = ref(false);
const evaluationList = ref<Evaluation[]>([]);

// 模拟数据 - manager需要评价的数据（managerName为当前用户且状态为self或colleague）
const mockEvaluationData: Evaluation[] = [
    {
        id: 1,
        department: '技术部',
        name: '张三',
        reviewDate: Date.now() - 7 * 24 * 60 * 60 * 1000, // 7天前
        colleagueName: '李四',
        managerName: currentUser || 'manager1', // 当前用户是主管
        score: undefined,
        additionalScore: undefined,
        comment: '',
        status: 'self', // 员工已自评，待同事评价
        createdAt: Date.now() - 7 * 24 * 60 * 60 * 1000,
        createdBy: '张三',
        updatedBy: '张三'
    },
    {
        id: 2,
        department: '销售部',
        name: '王五',
        reviewDate: Date.now() - 5 * 24 * 60 * 60 * 1000, // 5天前
        colleagueName: '赵六',
        managerName: currentUser || 'manager1', // 当前用户是主管
        score: undefined,
        additionalScore: undefined,
        comment: '',
        status: 'colleague', // 同事已评价，待主管评价
        createdAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
        createdBy: '王五',
        updatedBy: '赵六'
    },
    {
        id: 3,
        department: '技术部',
        name: '陈七',
        reviewDate: Date.now() - 3 * 24 * 60 * 60 * 1000, // 3天前
        colleagueName: '刘八',
        managerName: currentUser || 'manager1', // 当前用户是主管
        score: undefined,
        additionalScore: undefined,
        comment: '',
        status: 'self', // 员工已自评，待同事评价
        createdAt: Date.now() - 3 * 24 * 60 * 60 * 1000,
        createdBy: '陈七',
        updatedBy: '陈七'
    }
];

// 计算属性
const sortedEvaluationList = computed(() => {
    return [...evaluationList.value].sort((a, b) => {
        // 按创建时间倒序排列
        return (b.createdAt || 0) - (a.createdAt || 0);
    });
});

// 方法
const loadEvaluationList = async () => {
    loading.value = true;
    try {
        if (!currentUser) {
            message.error('用户信息获取失败');
            return;
        }

        // 构建查询参数
        const queryDto: EvaluationQueryDto = {
            page: 1,
            size: 100, // 获取所有待评价记录
            sortBy: 'createdAt',
            sortDirection: 'desc'
        };

        // 调用API获取主管待评价记录
        console.log(`output->currentUser`,currentUser)
        const response = await fyfcReviewApi.manager.getPendingEvaluations(currentUser, queryDto);

        if (response.success) {
            evaluationList.value = response.data.data;
            console.log('加载的待评价数据:', evaluationList.value);
            message.success('待评价列表加载成功');
        } else {
            console.error('API返回错误:', response.message);
            message.error(response.message || '加载待评价列表失败');

            // 如果API失败，使用模拟数据作为备选
            evaluationList.value = mockEvaluationData.filter(item =>
                item.managerName === currentUser &&
                (item.status === 'self' || item.status === 'colleague')
            );
            message.warning('使用模拟数据');
        }
    } catch (error) {
        console.error('加载待评价列表失败:', error);
        message.error('加载待评价列表失败');

        // 如果请求失败，使用模拟数据作为备选
        evaluationList.value = mockEvaluationData.filter(item =>
            item.managerName === currentUser &&
            (item.status === 'self' || item.status === 'colleague')
        );
        message.warning('网络异常，使用模拟数据');
    } finally {
        loading.value = false;
    }
};

const handleItemClick = (evaluation: Evaluation) => {
    // 点击列表项进行评价
    if (evaluation.id) {
        const encryptedUrl = generateEncryptedUrl('/fyfc/review/staff/edit', {
            id: evaluation.id,
            mode: 'edit'
        });
        router.push(encryptedUrl);
    }
};

const handleEvaluate = (evaluation: Evaluation) => {
    // 评价按钮点击
    if (evaluation.id) {
        const encryptedUrl = generateEncryptedUrl('/fyfc/review/staff/edit', {
            id: evaluation.id,
            mode: 'edit'
        });
        router.push(encryptedUrl);
    }
};

const formatDate = (timestamp?: number): string => {
    if (!timestamp) return '未设置';
    return new Date(timestamp).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
};

const formatDateTime = (timestamp?: number): string => {
    if (!timestamp) return '未知';
    return new Date(timestamp).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
};

const getStatusColor = (status?: string): string => {
    const statusColors: Record<string, string> = {
        'self': '#2080f0',
        'colleague': '#f0a020',
        'manager': '#18a058',
        'completed': '#52c41a'
    };
    return statusColors[status || 'self'] || '#d9d9d9';
};

const getStatusTagType = (status?: string): 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error' => {
    const statusTypes: Record<string, 'default' | 'primary' | 'info' | 'success' | 'warning' | 'error'> = {
        'self': 'primary',
        'colleague': 'warning',
        'manager': 'info',
        'completed': 'success'
    };
    return statusTypes[status || 'self'] || 'default';
};

// 生命周期
onMounted(() => {
    loadEvaluationList();
});
</script>

<style scoped>
/* 最外层包装器 - 重置所有样式 */
.page-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    margin: 0 !important;
    padding: 0 !important;
    background-color: #f5f5f5;
    overflow-y: auto;
}

/* 容器样式 - 垂直布局居中 */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 80px;
    box-sizing: border-box;
}

/* 内容区域样式 */
.content {
    width: 1000px;
    max-width: 90vw;
    background-color: transparent;
    text-align: center; /* 确保内容居中 */
}

.dashboard-card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: white;
    text-align: left; /* 卡片内容左对齐 */
}

.evaluation-item {
    transition: all 0.3s ease;
    border-radius: 6px;
    margin-bottom: 8px;
}

.evaluation-item:hover {
    background-color: #f5f5f5;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.evaluation-title {
    font-weight: 600;
    font-size: 16px;
    color: #333;
}

.status-tag {
    flex-shrink: 0;
}

.evaluation-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.detail-icon {
    color: #666;
    font-size: 16px;
}

.detail-label {
    color: #666;
    min-width: 70px;
}

.detail-value {
    color: #333;
    font-weight: 500;
}

.empty-state {
    padding: 60px 20px;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-top: 40px;
        width: 100vw;
    }

    .content {
        width: 95vw;
    }

    .evaluation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .evaluation-details {
        gap: 6px;
    }

    .detail-item {
        font-size: 13px;
    }

    .detail-label {
        min-width: 60px;
    }
}

/* 深度样式 */
:deep(.n-card-header) {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

:deep(.n-list-item) {
    padding: 16px;
}

:deep(.n-thing-header) {
    margin-bottom: 8px;
}

:deep(.n-thing-description) {
    margin-bottom: 12px;
}
</style>