<template>
    <div class="container" :style="'width:' + (screenWidth - 10) + 'px;'">
        <div class="content">
            <n-card title="绩效评价管理" :bordered="false">
                <!-- 数据统计信息 -->
                <div class="stats-info">
                    <span><strong>数据统计:</strong></span>
                    <span>当前页: {{ pagination.page }}/{{ pagination.pageCount }}</span>
                    <span>本页数据: {{ tableData.length }}条</span>
                    <span>总记录: {{ pagination.itemCount }}条</span>
                </div>

                <template #header-extra>
                    <n-space>
                        <LogoutButton :showSessionInfo="false" />
                        <n-button type="primary" size="small" @click="loadData">
                            刷新数据
                        </n-button>
                    </n-space>
                </template>

                <div class="dashboard-content">
                    <!-- 筛选区域 -->
                    <div class="filter-section">
                        <n-form inline :label-width="80" :show-feedback="false">
                            <n-form-item label="状态">
                                <n-select
                                    v-model:value="filters.status"
                                    :options="evaluationOptions.statusOptions"
                                    placeholder="选择状态"
                                    clearable
                                    style="width: 120px"
                                />
                            </n-form-item>
                            <n-form-item label="部门/项目">
                                <n-input
                                    v-model:value="filters.department"
                                    placeholder="输入部门/项目"
                                    clearable
                                    style="width: 140px"
                                />
                            </n-form-item>
                            <n-form-item label="姓名">
                                <n-input
                                    v-model:value="filters.name"
                                    placeholder="输入姓名"
                                    clearable
                                    style="width: 120px"
                                />
                            </n-form-item>
                            <n-form-item label="日期范围">
                                <n-date-picker
                                    v-model:value="filters.dateRange"
                                    type="daterange"
                                    placeholder="选择日期范围"
                                    clearable
                                    style="width: 260px"
                                />
                            </n-form-item>
                            <n-form-item label=" ">
                                <n-space :size="8">
                                    <n-button type="primary" @click="handleFilter">
                                        筛选
                                    </n-button>
                                    <n-button @click="handleResetFilter">
                                        重置
                                    </n-button>
                                    <n-button type="success" @click="handleExport" :loading="exportLoading">
                                        <template #icon>
                                            <n-icon><DownloadIcon /></n-icon>
                                        </template>
                                        导出Excel
                                    </n-button>
                                </n-space>
                            </n-form-item>
                        </n-form>
                    </div>

                    <n-divider />

                    <!-- 数据表格 -->
                    <n-data-table
                        :columns="columns"
                        :data="tableData"
                        :loading="loading"
                        :pagination="pagination"
                        :row-key="(row: EvaluationTableRow) => row.id || 0"
                        size="small"
                        :scroll-x="scrollX"
                        class="evaluation-table"
                        remote
                        striped
                    />
                </div>
            </n-card>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { generateEncryptedUrl } from '../../../../../utils/crypto';
import LogoutButton from '../../../../../components/common/LogoutButton.vue';
import {
    NCard,
    NDataTable,
    NButton,
    NIcon,
    NSpace,
    NForm,
    NFormItem,
    NSelect,
    NInput,
    NDatePicker,
    NDivider,
    useMessage
} from 'naive-ui';
import { Download as DownloadIcon } from '@vicons/ionicons5';
import type { Evaluation, EvaluationScore } from '../../../../../fconfig/fyfc/review';
import { evaluationOptions } from '../../../../../fconfig/fyfc/review/options';
import { createTableColumns, calculateTotalWidth, type EvaluationTableRow } from './tableConfig';
import { exportEvaluationToExcel } from '../../../../../utils/export/excelExport';
import { fyfcReviewApi, type EvaluationQueryDto, type EvaluationUpdateDto } from '../../../../../utils/FyfcReviewApi';
import { fyfcOssService } from '../../../../../utils/FyfcOssService';

// 路由和消息
const router = useRouter();
const message = useMessage();

// 状态持久化的键名
const STORAGE_KEY = 'fyfc_admin_dashboard_state';

// 状态持久化函数
const saveState = () => {
    const state = {
        filters: filters.value,
        pagination: {
            page: pagination.value.page,
            pageSize: pagination.value.pageSize
        },
        timestamp: Date.now()
    };
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(state));
    console.log('状态已保存:', state);
};

const loadState = () => {
    try {
        const savedState = sessionStorage.getItem(STORAGE_KEY);
        if (savedState) {
            const state = JSON.parse(savedState);

            // 检查状态是否过期（30分钟）
            const isExpired = Date.now() - state.timestamp > 30 * 60 * 1000;
            if (isExpired) {
                console.log('保存的状态已过期，使用默认状态');
                sessionStorage.removeItem(STORAGE_KEY);
                return false;
            }

            // 恢复筛选条件
            if (state.filters) {
                filters.value = { ...filters.value, ...state.filters };
            }

            // 恢复分页状态
            if (state.pagination) {
                pagination.value.page = state.pagination.page || 1;
                pagination.value.pageSize = state.pagination.pageSize || 10;
            }

            console.log('状态已恢复:', state);
            return true;
        }
    } catch (error) {
        console.error('恢复状态失败:', error);
        sessionStorage.removeItem(STORAGE_KEY);
    }
    return false;
};

const clearState = () => {
    sessionStorage.removeItem(STORAGE_KEY);
    console.log('状态已清除');
};

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const evaluationList = ref<Evaluation[]>([]);
const screenWidth = ref(window.innerWidth);

// 筛选条件
const filters = ref({
    status: null as string | null,
    department: '',
    name: '',
    dateRange: null as [number, number] | null
});

// 分页配置
const pagination = ref({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    showQuickJumper: true,
    itemCount: 0,
    pageCount: 1,
    onChange: (page: number) => {
        console.log('分页变化:', page);
        pagination.value.page = page;
        saveState(); // 保存状态
        loadData();
    },
    onUpdatePageSize: (pageSize: number) => {
        console.log('页面大小变化:', pageSize);
        pagination.value.pageSize = pageSize;
        pagination.value.page = 1;
        saveState(); // 保存状态
        loadData();
    }
});

// 类型映射：服务器类型 -> 前端类型
const mapServerTypeToFrontend = (serverType: string): string => {
    switch (serverType) {
        case 'self':
            return 'employee';
        case 'colleague':
            return 'colleague';
        case 'manager':
            return 'manager';
        default:
            return serverType;
    }
};

// 计算表格数据
const tableData = computed<EvaluationTableRow[]>(() => {
    return evaluationList.value.map(evaluation => {
        const scores = evaluation.scores || [];

        // 使用类型映射查找评分数据
        const employeeScore = scores.find((s: EvaluationScore) =>
            mapServerTypeToFrontend(s.type) === 'employee'
        )?.score || 0;
        const colleagueScore = scores.find((s: EvaluationScore) =>
            mapServerTypeToFrontend(s.type) === 'colleague'
        )?.score || 0;
        const managerScore = scores.find((s: EvaluationScore) =>
            mapServerTypeToFrontend(s.type) === 'manager'
        )?.score || 0;
        const managerEvaluator = scores.find((s: EvaluationScore) =>
            mapServerTypeToFrontend(s.type) === 'manager'
        )?.evaluator || '';
        const additionalScore = evaluation.additionalScore || 0;

        // 计算总分：根据同事评分情况调整比例
        // 当同事评分为0时：员工自评×10% + 主管评分×90% + 线上转发
        // 当同事评分不为0时：员工自评×10% + 同事评分×20% + 主管评分×70% + 线上转发
        let totalScore;
        if (colleagueScore === 0) {
            totalScore = employeeScore * 0.1 + managerScore * 0.9 + additionalScore;
        } else {
            totalScore = employeeScore * 0.1 + colleagueScore * 0.2 + managerScore * 0.7 + additionalScore;
        }

        return {
            ...evaluation,
            employeeScore,
            colleagueScore,
            managerScore,
            managerEvaluator,
            totalScore: totalScore || 0,
            // 保留原始的 calculatedScore，如果没有则使用 totalScore
            calculatedScore: evaluation.calculatedScore ?? (totalScore || 0)
        };
    });
});

// 计算是否应该固定操作列（屏幕不够宽时才固定）
const shouldFixActions = computed(() => {
    const totalWidth = calculateTotalWidth();
    const containerWidth = screenWidth.value - 40; // 减去容器边距 (20px * 2)
    return containerWidth < totalWidth; // 宽度不足时固定操作列
});

// 简化的滚动宽度设置
const scrollX = computed(() => {
    return 1454; // 固定滚动宽度，操作列保持164px
});

// 表格列配置
const columns = computed(() => createTableColumns(handleView, handleComplete, handleDelete, shouldFixActions.value));

// 计算最终得分的辅助函数
const calculateFinalScore = (row: EvaluationTableRow): number => {
    const employeeScore = row.employeeScore || 0;
    const colleagueScore = row.colleagueScore || 0;
    const managerScore = row.managerScore || 0;
    const additionalScore = row.additionalScore || 0;

    // 根据同事评分情况调整比例
    if (colleagueScore === 0) {
        return employeeScore * 0.1 + managerScore * 0.9 + additionalScore;
    } else {
        return employeeScore * 0.1 + colleagueScore * 0.2 + managerScore * 0.7 + additionalScore;
    }
};

// 数据加载
const loadData = async () => {
    console.log('开始加载数据...', {
        page: pagination.value.page,
        pageSize: pagination.value.pageSize,
        filters: filters.value
    });

    loading.value = true;
    try {
        const params: EvaluationQueryDto = {
            page: pagination.value.page,
            size: pagination.value.pageSize,
            status: filters.value.status || undefined,
            department: filters.value.department || undefined,
            name: filters.value.name || undefined,
            ...(filters.value.dateRange && {
                reviewDateStart: filters.value.dateRange[0],
                reviewDateEnd: filters.value.dateRange[1]
            })
        };

        const response = await fyfcReviewApi.admin.searchEvaluations(params);

        if (response.success) {
            evaluationList.value = response.data.data;
            pagination.value.itemCount = Number(response.data.total);
            pagination.value.pageCount = Math.ceil(response.data.total / pagination.value.pageSize);

            console.log('数据加载完成:', {
                currentPageData: evaluationList.value.length,
                total: response.data.total,
                page: pagination.value.page,
                pageSize: pagination.value.pageSize,
                pageCount: pagination.value.pageCount,
                itemCount: pagination.value.itemCount
            });

            message.success('数据加载成功');
        } else {
            message.error(response.message || '数据加载失败');
        }
    } catch (error) {
        console.error('加载数据失败:', error);
        message.error('加载数据失败');
    } finally {
        loading.value = false;
    }
};

// 筛选处理
const handleFilter = () => {
    pagination.value.page = 1;
    saveState(); // 保存状态
    loadData();
    message.info('筛选已应用');
};

// 重置筛选
const handleResetFilter = () => {
    filters.value = {
        status: null,
        department: '',
        name: '',
        dateRange: null
    };
    pagination.value.page = 1;
    clearState(); // 清除保存的状态
    loadData();
    message.info('筛选条件已重置');
};

// 查看详情
function handleView(row: EvaluationTableRow) {
    // 在跳转前保存当前状态
    saveState();

    // admin角色：如果评价未完成，使用编辑模式；如果已完成，使用查看模式
    const mode = row.status === 'completed' ? 'view' : 'edit';

    const encryptedUrl = generateEncryptedUrl('/fyfc/review/staff/edit', {
        id: row.id,
        mode: mode
    });
    router.push(encryptedUrl);
}

// 完成评价
async function handleComplete(row: EvaluationTableRow) {
    const confirmed = confirm(`确定要将 ${row.name} 的评价状态设置为已完成吗？`);

    if (!confirmed) {
        return;
    }

    try {
        // 先更新状态
        const statusResponse = await fyfcReviewApi.admin.updateEvaluationStatus(row.id!, 'completed', 'admin');

        if (statusResponse.success) {
            // 计算最终得分
            const calculatedScore = calculateFinalScore(row);

            // 使用 staff API 更新 calculatedScore（因为 EvaluationUpdateDto 包含此字段）
            const updateDto: EvaluationUpdateDto = {
                id: row.id!,
                calculatedScore: calculatedScore,
                updateReason: '管理员完成评价时更新最终得分',
                sendNotification: false
            };

            try {
                await fyfcReviewApi.staff.updateEvaluation(updateDto, 'admin');
                console.log('最终得分更新成功:', calculatedScore);
            } catch (updateError) {
                console.warn('更新最终得分失败，但状态更新成功:', updateError);
            }

            // 更新本地数据
            const index = evaluationList.value.findIndex(item => item.id === row.id);
            if (index !== -1) {
                evaluationList.value[index].status = 'completed';
                evaluationList.value[index].updatedBy = 'admin';
                evaluationList.value[index].calculatedScore = calculatedScore;
            }
            message.success('状态更新成功');
        } else {
            message.error(statusResponse.message || '状态更新失败');
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        message.error('更新状态失败');
    }
}

// 删除评价
async function handleDelete(row: EvaluationTableRow) {
    const confirmed = confirm(`确定要删除 ${row.name} 的评价记录吗？\n\n注意：此操作不可恢复，包括相关的附件文件！`);

    if (!confirmed) {
        return;
    }

    try {
        // 1. 首先获取评价详情，检查是否有附件
        console.log('开始删除评价，先检查附件...', row.id);
        let attachments: any[] = [];

        // 从当前行数据中提取附件信息
        if (row.attachments) {
            try {
                attachments = JSON.parse(row.attachments);
            } catch (error) {
                console.warn('解析附件数据失败，尝试从服务器获取:', error);
            }
        }

        // 如果本地没有附件信息，从服务器获取
        if (attachments.length === 0) {
            try {
                attachments = await fyfcOssService.getEvaluationAttachments(row.id!);
            } catch (error) {
                console.warn('从服务器获取附件列表失败:', error);
            }
        }

        // 2. 删除所有附件
        if (attachments.length > 0) {
            console.log(`发现 ${attachments.length} 个附件，开始删除...`);
            let deletedCount = 0;
            let failedCount = 0;

            for (const attachment of attachments) {
                try {
                    const success = await fyfcOssService.deleteFile(
                        attachment.fileKey,
                        row.id!,
                        'admin',
                        attachment.bucketName
                    );
                    if (success) {
                        deletedCount++;
                        console.log(`附件删除成功: ${attachment.fileName}`);
                    } else {
                        failedCount++;
                        console.warn(`附件删除失败: ${attachment.fileName}`);
                    }
                } catch (error) {
                    failedCount++;
                    console.error(`附件删除异常: ${attachment.fileName}`, error);
                }
            }

            console.log(`附件删除完成: 成功 ${deletedCount} 个，失败 ${failedCount} 个`);

            // 如果有附件删除失败，给用户提示但继续删除评价记录
            if (failedCount > 0) {
                message.warning(`部分附件删除失败 (${failedCount}/${attachments.length})，但将继续删除评价记录`);
            }
        }

        // 3. 删除评价记录
        console.log('开始删除评价记录...');
        const response = await fyfcReviewApi.admin.deleteEvaluation(row.id!, 'admin');

        if (response.success && response.data > 0) {
            // 从本地数据中移除
            const index = evaluationList.value.findIndex(item => item.id === row.id);
            if (index !== -1) {
                evaluationList.value.splice(index, 1);
            }

            // 更新分页信息
            pagination.value.itemCount = Math.max(0, pagination.value.itemCount - 1);
            pagination.value.pageCount = Math.ceil(pagination.value.itemCount / pagination.value.pageSize);

            // 如果当前页没有数据且不是第一页，则跳转到上一页
            if (evaluationList.value.length === 0 && pagination.value.page > 1) {
                pagination.value.page = pagination.value.page - 1;
                await loadData();
            }

            const attachmentMsg = attachments.length > 0 ? `（包含 ${attachments.length} 个附件）` : '';
            message.success(`删除成功${attachmentMsg}`);
        } else {
            message.error(response.message || '删除失败');
        }
    } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
    }
}

// 导出Excel
const handleExport = async () => {
    exportLoading.value = true;
    try {
        // 获取所有符合筛选条件的数据
        const exportParams: EvaluationQueryDto = {
            page: 1,
            size: 999999,
            status: filters.value.status || undefined,
            department: filters.value.department || undefined,
            name: filters.value.name || undefined,
            ...(filters.value.dateRange && {
                startDate: filters.value.dateRange[0],
                endDate: filters.value.dateRange[1]
            })
        };

        console.log('开始导出，获取所有数据...', exportParams);
        const response = await fyfcReviewApi.admin.exportEvaluations(exportParams);

        if (!response.success) {
            message.error(response.message || '导出数据获取失败');
            return;
        }

        // 计算导出数据
        const allTableData = response.data.map((evaluation: any) => {
            const scores = evaluation.scores || [];

            // 使用类型映射查找评分数据
            const employeeScore = scores.find((s: EvaluationScore) =>
                mapServerTypeToFrontend(s.type) === 'employee'
            )?.score || 0;
            const colleagueScore = scores.find((s: EvaluationScore) =>
                mapServerTypeToFrontend(s.type) === 'colleague'
            )?.score || 0;
            const managerScore = scores.find((s: EvaluationScore) =>
                mapServerTypeToFrontend(s.type) === 'manager'
            )?.score || 0;
            const managerEvaluator = scores.find((s: EvaluationScore) =>
                mapServerTypeToFrontend(s.type) === 'manager'
            )?.evaluator || '';
            const additionalScore = evaluation.additionalScore || 0;

            // 计算总分：根据同事评分情况调整比例
            // 当同事评分为0时：员工自评×10% + 主管评分×90% + 线上转发
            // 当同事评分不为0时：员工自评×10% + 同事评分×20% + 主管评分×70% + 线上转发
            let totalScore;
            if (colleagueScore === 0) {
                totalScore = employeeScore * 0.1 + managerScore * 0.9 + additionalScore;
            } else {
                totalScore = employeeScore * 0.1 + colleagueScore * 0.2 + managerScore * 0.7 + additionalScore;
            }

            return {
                ...evaluation,
                employeeScore,
                colleagueScore,
                managerScore,
                managerEvaluator,
                totalScore: totalScore || 0,
                // 保留原始的 calculatedScore，如果没有则使用 totalScore
                calculatedScore: evaluation.calculatedScore ?? (totalScore || 0)
            };
        });

        console.log(`导出数据准备完成，共 ${allTableData.length} 条记录`);

        // 导出Excel
        const result = exportEvaluationToExcel(allTableData);

        message.success(`导出成功，共导出 ${result.recordCount} 条记录`);
    } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
    } finally {
        exportLoading.value = false;
    }
};

// 窗口大小变化监听
const handleResize = () => {
    screenWidth.value = window.innerWidth;
};

// 页面加载时获取数据
onMounted(() => {
    console.log('管理员页面开始加载...');

    // 尝试恢复保存的状态
    const hasRestoredState = loadState();
    if (hasRestoredState) {
        console.log('已恢复保存的筛选和分页状态');
        message.info('已恢复上次的筛选和分页状态');
    }

    loadData();
    console.log('数据加载函数调用成功');

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);
});

// 监听筛选条件变化，自动保存状态
watch(
    () => filters.value,
    () => {
        // 延迟保存，避免频繁操作
        setTimeout(() => {
            saveState();
        }, 500);
    },
    { deep: true }
);

// 页面卸载时清理监听器
onUnmounted(() => {
    window.removeEventListener('resize', handleResize);
});
</script>

<style scoped>
.container {
    padding: 1px;
    min-height: 100vh;
    background-color: #f5f5f5;
    box-sizing: border-box;
}

.content {
    width: 100%;
    margin: 0;
}

.dashboard-content {
    padding: 0;
}

/* 卡片占满容器 */
.dashboard-card {
    width: 100%;
    margin: 0;
}

/* 缩小卡片内边距 */
.dashboard-card :deep(.n-card-header) {
    padding: 16px 12px 12px 12px; /* 左右从20px改为12px */
}

.dashboard-card :deep(.n-card__content) {
    padding: 12px 12px 16px 12px; /* 左右从20px改为12px */
}

/* 统计信息样式 */
.stats-info {
    background: #f8f9fa;
    padding: 8px 12px;
    margin-bottom: 16px;
    border-radius: 6px;
    border-left: 4px solid #18a058;
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #666;
}

.stats-info span:first-child {
    color: #333;
}

.filter-section {
    padding: 16px 0;
}

/* 筛选表单样式优化 */
.filter-section :deep(.n-form) {
    align-items: flex-end;
}

.filter-section :deep(.n-form-item) {
    margin-bottom: 8px;
}

.filter-section :deep(.n-form-item-label) {
    font-weight: 500;
    color: #333;
}

.evaluation-table {
    margin-top: 16px;
    width: 100%;
}

/* 表格自适应优化 */
.evaluation-table :deep(.n-data-table-wrapper) {
    width: 100%;
}

/* 强制固定列样式 - 确保在所有屏幕尺寸下都生效 */
.evaluation-table :deep(.n-data-table-th--fixed-right) {
    position: sticky !important;
    right: 0 !important;
    z-index: 3 !important;
    background-color: white !important;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
}

.evaluation-table :deep(.n-data-table-td--fixed-right) {
    position: sticky !important;
    right: 0 !important;
    z-index: 2 !important;
    background-color: white !important;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1) !important;
}

/* 备注列特殊处理 */
.evaluation-table :deep(.n-data-table-td[data-col-key="comment"]) {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-top: 1px;
    }

    .content {
        width: 97vw;
    }

    /* 移动端卡片占满容器 */
    .dashboard-card {
        width: 100%;
        margin: 0;
    }

    .dashboard-card :deep(.n-card) {
        width: 100%;
    }

    .filter-section {
        padding: 12px 0;
    }

    /* 移动端筛选表单布局 */
    .filter-section :deep(.n-form) {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-section :deep(.n-form-item) {
        margin-bottom: 12px;
        width: 100%;
    }

    .filter-section :deep(.n-form-item-blank) {
        min-width: 100%;
    }

    .filter-section :deep(.n-input),
    .filter-section :deep(.n-select),
    .filter-section :deep(.n-date-picker) {
        width: 100% !important;
    }

    .filter-section :deep(.n-space) {
        width: 100%;
        justify-content: space-around;
    }

    .filter-section :deep(.n-button) {
        flex: 1;
        min-width: 80px;
    }
}
</style>
