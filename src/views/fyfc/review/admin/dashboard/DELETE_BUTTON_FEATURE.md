# Admin Dashboard删除按钮功能

## 🎯 **功能描述**

在FYFC评审系统的管理员仪表板表格中添加删除按钮，允许管理员删除评价记录。

## 🔧 **实现方案**

### **1. 表格配置修改 (tableConfig.ts)**

#### **导入删除图标**
```typescript
import { Eye as ViewIcon, Checkmark as CompleteIcon, Create as EditIcon, Trash as DeleteIcon } from '@vicons/ionicons5';
```

#### **添加删除处理函数参数**
```typescript
export const createTableColumns = (
    handleView: (row: EvaluationTableRow) => void,
    handleComplete: (row: EvaluationTableRow) => void,
    handleDelete: (row: EvaluationTableRow) => void,  // 新增
    shouldFixActions: boolean = true
): DataTableColumns<EvaluationTableRow> => [
```

#### **操作列宽度调整**
```typescript
// 操作列保持164px宽度
{
    title: '操作',
    key: 'actions',
    minWidth: 82,
    width: 164,  // 保持原有宽度
    ...
}
```

#### **添加删除按钮**
```typescript
h(NButton, {
    size: 'small',
    type: 'error',
    onClick: () => handleDelete(row)
}, {
    default: () => '删除',
    icon: () => h(NIcon, null, { default: () => h(DeleteIcon) })
})
```

### **2. 主页面修改 (index.vue)**

#### **添加删除处理函数**
```typescript
// 删除评价
async function handleDelete(row: EvaluationTableRow) {
    const confirmed = confirm(`确定要删除 ${row.name} 的评价记录吗？\n\n注意：此操作不可恢复！`);

    if (!confirmed) {
        return;
    }

    try {
        // 1. 首先检查并删除相关附件
        let attachments: any[] = [];

        // 从当前行数据中提取附件信息
        if (row.attachments) {
            try {
                attachments = JSON.parse(row.attachments);
            } catch (error) {
                console.warn('解析附件数据失败，尝试从服务器获取:', error);
            }
        }

        // 如果本地没有附件信息，从服务器获取
        if (attachments.length === 0) {
            try {
                attachments = await fyfcOssService.getEvaluationAttachments(row.id!);
            } catch (error) {
                console.warn('从服务器获取附件列表失败:', error);
            }
        }

        // 删除所有附件
        if (attachments.length > 0) {
            for (const attachment of attachments) {
                try {
                    await fyfcOssService.deleteFile(
                        attachment.fileKey,
                        row.id!,
                        'admin',
                        attachment.bucketName
                    );
                } catch (error) {
                    console.error(`附件删除失败: ${attachment.fileName}`, error);
                }
            }
        }

        // 2. 删除评价记录（使用批量删除API）
        const response = await fyfcReviewApi.admin.deleteEvaluation(row.id!, 'admin');

        if (response.success) {
            // 从本地数据中移除
            const index = evaluationList.value.findIndex(item => item.id === row.id);
            if (index !== -1) {
                evaluationList.value.splice(index, 1);
            }
            
            // 更新分页信息
            pagination.value.itemCount = Math.max(0, pagination.value.itemCount - 1);
            pagination.value.pageCount = Math.ceil(pagination.value.itemCount / pagination.value.pageSize);
            
            // 如果当前页没有数据且不是第一页，则跳转到上一页
            if (evaluationList.value.length === 0 && pagination.value.page > 1) {
                pagination.value.page = pagination.value.page - 1;
                await loadData();
            }
            
            message.success('删除成功');
        } else {
            message.error(response.message || '删除失败');
        }
    } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
    }
}
```

#### **更新表格列配置调用**
```typescript
// 表格列配置
const columns = computed(() => createTableColumns(handleView, handleComplete, handleDelete, shouldFixActions.value));
```

#### **更新滚动宽度**
```typescript
// 简化的滚动宽度设置
const scrollX = computed(() => {
    return 1364; // 固定滚动宽度，操作列保持164px
});
```

## 🚀 **功能特性**

### **1. 附件处理**
- 删除前自动检查评价记录的附件
- 先删除所有相关的OSS文件
- 支持从本地数据和服务器获取附件列表
- 附件删除失败不影响评价记录删除

### **2. 安全确认**
- 删除前显示确认对话框
- 明确提示操作不可恢复，包括附件文件
- 显示被删除记录的姓名

### **3. 智能分页处理**
- 删除后自动更新分页信息
- 当前页无数据时自动跳转到上一页
- 保持分页状态的一致性

### **4. 用户体验优化**
- 使用红色error类型按钮表示危险操作
- 使用垃圾桶图标直观表示删除功能
- 操作成功后显示成功提示，包含附件删除信息

### **5. 数据同步**
- 删除成功后立即更新本地数据
- 避免重新加载整个页面
- 保持用户当前的筛选和分页状态

## 📊 **按钮布局**

操作列现在包含以下按钮：

| 状态 | 查看/编辑按钮 | 完成按钮 | 删除按钮 |
|------|---------------|----------|----------|
| **未完成** | 🟡 编辑 (warning) | 🟢 完成 (success) | 🔴 删除 (error) |
| **已完成** | 🔵 查看 (info) | - | 🔴 删除 (error) |

## 🔒 **权限控制**

- 只有管理员角色可以访问删除功能
- 删除操作使用'admin'作为操作者标识
- 所有状态的评价都可以被删除（包括已完成的）

## 🛡️ **安全考虑**

### **1. 确认机制**
```typescript
const confirmed = confirm(`确定要删除 ${row.name} 的评价记录吗？\n\n注意：此操作不可恢复，包括相关的附件文件！`);
```

### **2. 附件安全删除**
```typescript
// 先删除所有附件
if (attachments.length > 0) {
    for (const attachment of attachments) {
        await fyfcOssService.deleteFile(
            attachment.fileKey,
            row.id!,
            'admin',
            attachment.bucketName
        );
    }
}
```

### **3. 错误处理**
- API调用失败时显示错误信息
- 网络异常时的友好提示
- 操作失败不影响页面状态

### **4. 数据一致性**
- 删除成功后立即更新本地状态
- 分页信息同步更新
- 避免数据不一致问题

## 🔄 **API调用**

使用管理员专用的删除API（内部调用批量删除）：
```typescript
fyfcReviewApi.admin.deleteEvaluation(row.id!, 'admin')
```

该API内部调用批量删除接口：
```typescript
// 前端API实现
async deleteEvaluation(id: number, operator: string): Promise<ApiResponse<number>> {
    return this.batchDeleteEvaluations([id], operator);
}
```

对应后端接口：
```
DELETE /admin/batch
Content-Type: application/json
Body: [evaluationId]
Query: ?operator=admin
```

## 📝 **使用场景**

### **场景1: 删除错误创建的评价**
1. 管理员发现某个评价记录创建错误
2. 点击该记录的删除按钮
3. 确认删除操作
4. 系统删除记录并更新列表

### **场景2: 清理测试数据**
1. 管理员需要清理测试环境的数据
2. 批量删除测试评价记录
3. 保持生产数据的整洁

### **场景3: 处理重复记录**
1. 发现重复的评价记录
2. 删除多余的记录
3. 保持数据的唯一性

## ⚠️ **注意事项**

1. **不可恢复**: 删除操作是永久性的，无法撤销
2. **权限限制**: 只有管理员可以执行删除操作
3. **数据关联**: 删除评价可能影响相关的评分和附件数据
4. **审计日志**: 建议在后端记录删除操作的审计日志

## 🔮 **后续优化建议**

### **1. 批量删除**
考虑添加批量删除功能：
```typescript
// 选择多个记录进行批量删除
const selectedRows = ref<EvaluationTableRow[]>([]);
```

### **2. 软删除**
考虑实现软删除机制：
```typescript
// 标记为删除而不是物理删除
status: 'deleted'
```

### **3. 删除权限细化**
根据评价状态限制删除权限：
```typescript
// 只允许删除特定状态的评价
const canDelete = !['completed'].includes(row.status);
```

### **4. 删除确认增强**
使用更友好的确认对话框：
```typescript
// 使用Naive UI的对话框替代原生confirm
const dialog = useDialog();
```

## 📋 **总结**

成功在FYFC评审系统管理员仪表板中添加了删除功能：

1. ✅ **功能完整** - 删除按钮、确认对话框、API调用、附件处理
2. ✅ **用户体验** - 直观的图标、清晰的提示、智能分页
3. ✅ **安全可靠** - 确认机制、错误处理、数据同步、附件清理
4. ✅ **界面美观** - 合理的布局、一致的样式、响应式设计
5. ✅ **数据完整性** - 先删除附件再删除记录，确保数据一致性

管理员现在可以方便地删除不需要的评价记录（包括相关附件），提高了系统的管理效率！🎉
