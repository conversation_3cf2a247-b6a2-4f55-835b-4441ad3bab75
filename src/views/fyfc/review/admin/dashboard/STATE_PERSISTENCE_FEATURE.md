# Admin Dashboard状态持久化功能

## 🎯 **功能描述**

在FYFC评审系统的管理员仪表板中实现状态持久化，解决用户从编辑页面返回时丢失筛选条件和分页状态的问题。

## 🔧 **实现方案**

### **1. 状态持久化机制**

使用 `sessionStorage` 保存和恢复用户的筛选条件和分页状态：

```typescript
// 状态持久化的键名
const STORAGE_KEY = 'fyfc_admin_dashboard_state';

// 保存状态
const saveState = () => {
    const state = {
        filters: filters.value,
        pagination: {
            page: pagination.value.page,
            pageSize: pagination.value.pageSize
        },
        timestamp: Date.now()
    };
    sessionStorage.setItem(STORAGE_KEY, JSON.stringify(state));
};

// 恢复状态
const loadState = () => {
    try {
        const savedState = sessionStorage.getItem(STORAGE_KEY);
        if (savedState) {
            const state = JSON.parse(savedState);
            
            // 检查状态是否过期（30分钟）
            const isExpired = Date.now() - state.timestamp > 30 * 60 * 1000;
            if (isExpired) {
                sessionStorage.removeItem(STORAGE_KEY);
                return false;
            }
            
            // 恢复筛选条件和分页状态
            if (state.filters) {
                filters.value = { ...filters.value, ...state.filters };
            }
            if (state.pagination) {
                pagination.value.page = state.pagination.page || 1;
                pagination.value.pageSize = state.pagination.pageSize || 10;
            }
            
            return true;
        }
    } catch (error) {
        console.error('恢复状态失败:', error);
        sessionStorage.removeItem(STORAGE_KEY);
    }
    return false;
};
```

### **2. 自动保存触发点**

#### **筛选操作**
```typescript
const handleFilter = () => {
    pagination.value.page = 1;
    saveState(); // 保存状态
    loadData();
    message.info('筛选已应用');
};
```

#### **分页操作**
```typescript
onChange: (page: number) => {
    pagination.value.page = page;
    saveState(); // 保存状态
    loadData();
},
onUpdatePageSize: (pageSize: number) => {
    pagination.value.pageSize = pageSize;
    pagination.value.page = 1;
    saveState(); // 保存状态
    loadData();
}
```

#### **跳转编辑页面前**
```typescript
function handleView(row: EvaluationTableRow) {
    // 在跳转前保存当前状态
    saveState();
    
    const mode = row.status === 'completed' ? 'view' : 'edit';
    const encryptedUrl = generateEncryptedUrl('/fyfc/review/staff/edit', {
        id: row.id,
        mode: mode
    });
    router.push(encryptedUrl);
}
```

#### **筛选条件变化监听**
```typescript
// 监听筛选条件变化，自动保存状态
watch(
    () => filters.value,
    () => {
        // 延迟保存，避免频繁操作
        setTimeout(() => {
            saveState();
        }, 500);
    },
    { deep: true }
);
```

### **3. 状态恢复时机**

#### **页面加载时**
```typescript
onMounted(() => {
    // 尝试恢复保存的状态
    const hasRestoredState = loadState();
    if (hasRestoredState) {
        message.info('已恢复上次的筛选和分页状态');
    }
    
    loadData();
    window.addEventListener('resize', handleResize);
});
```

### **4. 状态清除机制**

#### **重置筛选时**
```typescript
const handleResetFilter = () => {
    filters.value = {
        status: null,
        department: '',
        name: '',
        dateRange: null
    };
    pagination.value.page = 1;
    clearState(); // 清除保存的状态
    loadData();
    message.info('筛选条件已重置');
};
```

## 🚀 **功能特性**

### **1. 智能过期机制**
- 状态保存时间戳，30分钟后自动过期
- 过期状态自动清除，避免数据污染
- 确保状态的时效性

### **2. 完整状态保存**
- **筛选条件**: 状态、部门、姓名、日期范围
- **分页状态**: 当前页码、每页条数
- **时间戳**: 用于过期检查

### **3. 用户体验优化**
- 从编辑页面返回时自动恢复状态
- 显示友好的恢复提示信息
- 延迟保存避免频繁操作

### **4. 错误处理**
- JSON解析异常处理
- 存储访问异常处理
- 自动清理损坏的状态数据

## 📊 **使用场景**

### **场景1: 编辑后返回**
1. 用户在第3页，筛选了"已完成"状态的记录
2. 点击某条记录的"编辑"按钮
3. 在编辑页面完成操作后返回
4. 自动恢复到第3页的"已完成"筛选状态

### **场景2: 页面刷新**
1. 用户设置了复杂的筛选条件
2. 意外刷新了页面
3. 页面重新加载后自动恢复筛选状态
4. 用户无需重新设置筛选条件

### **场景3: 状态过期**
1. 用户离开页面超过30分钟
2. 再次访问时状态已过期
3. 系统自动清除过期状态
4. 使用默认状态重新开始

## 🔒 **数据安全**

### **1. 存储范围**
- 使用 `sessionStorage`，仅在当前标签页有效
- 关闭标签页时自动清理数据
- 不会影响其他标签页或窗口

### **2. 数据结构**
```typescript
interface DashboardState {
    filters: {
        status: string | null;
        department: string;
        name: string;
        dateRange: [number, number] | null;
    };
    pagination: {
        page: number;
        pageSize: number;
    };
    timestamp: number;
}
```

### **3. 隐私保护**
- 不保存敏感的用户数据
- 只保存筛选条件和分页参数
- 自动过期机制保护隐私

## ⚡ **性能优化**

### **1. 延迟保存**
```typescript
// 延迟500ms保存，避免频繁操作
setTimeout(() => {
    saveState();
}, 500);
```

### **2. 深度监听优化**
- 只监听必要的状态变化
- 使用防抖机制减少保存频率
- 避免不必要的存储操作

### **3. 存储大小控制**
- 只保存必要的状态信息
- 定期清理过期状态
- 控制存储数据的大小

## 🛠️ **扩展性**

### **1. 添加新的状态字段**
```typescript
// 在saveState函数中添加新字段
const state = {
    filters: filters.value,
    pagination: { ... },
    newField: newValue.value, // 新增字段
    timestamp: Date.now()
};
```

### **2. 自定义过期时间**
```typescript
// 可配置的过期时间
const EXPIRY_TIME = 30 * 60 * 1000; // 30分钟
const isExpired = Date.now() - state.timestamp > EXPIRY_TIME;
```

### **3. 多页面状态管理**
```typescript
// 为不同页面使用不同的存储键
const STORAGE_KEY = `fyfc_${pageName}_dashboard_state`;
```

## 📝 **总结**

成功实现了管理员仪表板的状态持久化功能：

1. ✅ **问题解决** - 编辑后返回不再丢失筛选和分页状态
2. ✅ **用户体验** - 自动保存和恢复，无需用户手动操作
3. ✅ **数据安全** - 使用sessionStorage，自动过期清理
4. ✅ **性能优化** - 延迟保存，避免频繁操作
5. ✅ **错误处理** - 完善的异常处理和数据清理机制

用户现在可以在编辑页面和仪表板之间自由切换，而不会丢失当前的工作状态！🎉
