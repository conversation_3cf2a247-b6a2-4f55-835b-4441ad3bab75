<template>
    <div class="page-wrapper">
        <div class="container">
            <!-- 临时角色切换器 - 仅开发环境显示 -->
            <div v-if="isDevelopment" class="temp-role-switcher">
                <n-text strong style="margin-right: 12px;">临时角色切换 (开发测试):</n-text>
                <n-radio-group v-model:value="tempUserRole" @update:value="handleRoleChange">
                    <n-radio value="employee">员工</n-radio>
                    <n-radio value="colleague">同事</n-radio>
                    <n-radio value="manager">主管</n-radio>
                    <n-radio value="admin">管理员</n-radio>
                </n-radio-group>
                <n-text depth="3" style="font-size: 12px; margin-left: 12px;">
                    模式: {{ pageMode }} | ID: {{ evaluationId || '新增' }} | 可编辑: {{ currentUserRole.canEdit }}
                </n-text>
                <br />
                <n-text depth="3" style="font-size: 11px;">
                    评分数据: {{ evaluationData.scores?.length || 0 }}条 |
                    类型: {{ evaluationData.scores?.map(s => s.type).join(', ') || '无' }}
                </n-text>
            </div>

            <div class="content">
                <EvaluationEditorNew
                    v-model="evaluationData"
                    :title="pageTitle"
                    :user-role="currentUserRole"
                    @save="handleSave"
                    @reset="handleReset"
                    @preview="handlePreview"
                    @back="handleBack"
                    @scoreSubmitted="handleScoreSubmitted"
                />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { NText, NRadio, NRadioGroup, useMessage } from 'naive-ui';
import EvaluationEditorNew, { type EvaluationData, type UserRoleConfig } from '../../../../../components/fyfc/review/EvaluationEditorNew.vue';
import type { UserRole } from '../../../../../fconfig/fyfc/review';
import { parseEncryptedRoute } from '../../../../../utils/crypto';
import { fyfcReviewApi, type EvaluationFormDto, type EvaluationUpdateDto } from '../../../../../utils/FyfcReviewApi';
import { useUserContext } from '../../../../../utils/UserContext';

// 路由和消息
const route = useRoute();
const router = useRouter();
const message = useMessage();

// 获取用户上下文
const {
    getCurrentUsername,
    getCurrentDisplayName,
    getEvaluationRole,
    hasEvaluationPermission,
    isEmployeeManager,
    getUserAllRoles,
    hasRoles
} = useUserContext();

// 环境检测
// const isDevelopment = import.meta.env.DEV || import.meta.env.MODE === 'development';
const isDevelopment = false;
// 解析页面参数（支持加密和明文）
const parseRouteParams = () => {
    // 优先尝试解析加密参数
    const encryptedParams = parseEncryptedRoute(route);
    if (encryptedParams) {
        console.log('解析加密参数成功:', encryptedParams);
        return {
            id: encryptedParams.id?.toString() || null,
            mode: encryptedParams.mode as 'edit' | 'view' || 'edit'
        };
    }

    // 回退到明文参数（兼容性）
    return {
        id: route.query.id as string || null,
        mode: route.query.mode as 'edit' | 'view' || 'edit'
    };
};

// 页面参数
const routeParams = parseRouteParams();
const evaluationId = ref<string | null>(routeParams.id);
const pageMode = ref<'edit' | 'view' | 'create'>(
    evaluationId.value ? routeParams.mode : 'create'
);

// 加载状态
const loading = ref(false);

// 页面标题
const pageTitle = computed(() => {
    switch (pageMode.value) {
        case 'create':
            return '新增绩效评价';
        case 'edit':
            return '编辑绩效评价';
        case 'view':
            return '查看绩效评价';
        default:
            return '绩效评价';
    }
});

// 获取当前用户信息
const currentUsername = getCurrentUsername();
const currentUser = getCurrentDisplayName();
const systemUserRole = getEvaluationRole();



// 临时角色切换（用于测试）
const tempUserRole = ref<UserRole>(systemUserRole);

// 临时角色切换方法
const handleRoleChange = (role: UserRole) => {
    tempUserRole.value = role;
};

// 动态计算当前用户角色
const currentUserRole = computed<UserRoleConfig>(() => {
    let roleType: UserRole = 'unknown';
    let canEdit = false;

    // 分享链接模式：允许查看但不允许编辑
    if (pageMode.value === 'view') {
        // 查看模式下，根据用户权限确定角色类型，但始终允许查看
        const userRole = getEvaluationRole();

        // 获取用户的所有角色
        const allRoles = getUserAllRoles();
        const hasEmployeeRole = allRoles.includes('employee');
        const hasManagerRole = allRoles.includes('manager');
        const hasAdminRole = allRoles.includes('admin');

        if (hasAdminRole) {
            roleType = 'admin';
        } else if (hasManagerRole && hasEmployeeRole) {
            // 同时拥有管理者和员工权限的用户
            if (!evaluationId.value) {
                roleType = 'employee';
            } else if (evaluationData.value.name) {
                if (currentUser === evaluationData.value.name) {
                    roleType = 'employee'; // 本人
                } else {
                    // 他人的评价：根据evaluation中的邀请字段判断角色
                    if (currentUser === evaluationData.value.colleagueName) {
                        roleType = 'colleague'; // 当前用户是被邀请的同事
                    } else if (currentUser === evaluationData.value.managerName) {
                        roleType = 'manager'; // 当前用户是被邀请的主管
                    } else {
                        // 如果都不匹配，默认为manager（向后兼容）
                        roleType = 'manager';
                    }
                }
            } else {
                // 数据未加载时，默认为manager，等数据加载后会重新计算
                roleType = 'manager';
            }
        } else if (hasManagerRole) {
            roleType = 'manager';
        } else if (hasEmployeeRole || userRole === 'unknown') {
            if (!evaluationId.value) {
                roleType = 'employee';
            } else if (evaluationData.value.name) {
                if (currentUser === evaluationData.value.name) {
                    roleType = 'employee';
                } else {
                    roleType = 'colleague';
                }
            } else {
                roleType = 'colleague';
            }
        } else {
            // 即使没有权限，在查看模式下也允许查看，默认为colleague角色
            roleType = 'colleague';
        }

        return {
            type: roleType,
            canEdit: false, // 查看模式不允许编辑
            canView: true   // 始终允许查看
        };
    }

    // 编辑模式：检查权限
    // 注意：同事评价者和主管可能没有系统级的绩效评价权限，
    // 但可以对特定的evaluation进行评价，所以这里不做严格的权限检查
    const userRole = getEvaluationRole();

    // 在开发环境下，允许临时角色切换
    if (isDevelopment) {
        roleType = tempUserRole.value;
        canEdit = true; // 开发环境下允许编辑
    } else {
        // 生产环境下，根据实际情况计算角色（userRole已在上面获取）
        // 获取用户的所有角色
        const allRoles = getUserAllRoles();
        const hasEmployeeRole = allRoles.includes('employee');
        const hasManagerRole = allRoles.includes('manager');
        const hasAdminRole = allRoles.includes('admin');

        // 多角色处理逻辑
        if (hasAdminRole) {
            // 管理员角色优先级最高
            roleType = 'admin';
            canEdit = true;
        } else if (hasManagerRole && hasEmployeeRole) {
            // 同时拥有管理者和员工权限的用户
            if (!evaluationId.value) {
                // 新增模式：默认为本人自评（员工角色）
                roleType = 'employee';
                canEdit = true;
            } else if (evaluationData.value.name) {
                // 编辑模式且数据已加载：根据name和邀请字段判断
                if (currentUser === evaluationData.value.name) {
                    roleType = 'employee'; // 本人，可以自评
                    canEdit = true;
                } else {
                    // 他人的评价：根据evaluation中的邀请字段判断角色
                    if (currentUser === evaluationData.value.colleagueName) {
                        roleType = 'colleague'; // 当前用户是被邀请的同事
                        canEdit = true;
                    } else if (currentUser === evaluationData.value.managerName) {
                        roleType = 'manager'; // 当前用户是被邀请的主管
                        canEdit = true;
                    } else {
                        // 如果都不匹配，默认为manager（向后兼容）
                        roleType = 'manager';
                        canEdit = true;
                    }
                }
            } else {
                // 编辑模式但数据未加载：默认为manager，等数据加载后会重新计算
                roleType = 'manager';
                canEdit = true;
            }
        } else if (hasManagerRole) {
            // 仅有管理者权限
            roleType = 'manager';
            canEdit = true;
        } else if (hasEmployeeRole || userRole === 'unknown') {
            // 仅有员工权限或未知角色
            if (!evaluationId.value) {
                // 新增模式：默认为本人自评
                roleType = 'employee';
                canEdit = true;
            } else if (evaluationData.value.name) {
                // 编辑模式且数据已加载：根据name判断
                if (currentUser === evaluationData.value.name) {
                    roleType = 'employee'; // 本人，可以自评
                    canEdit = true;
                } else {
                    roleType = 'colleague'; // 他人，同事评价
                    canEdit = true;
                }
            } else {
                // 编辑模式但数据未加载：默认为colleague，等数据加载后会重新计算
                roleType = 'colleague';
                canEdit = true;
            }
        }
    }

    const result = {
        type: roleType,
        canEdit,
        canView: true
    };
    return result;
});





// 评价数据（初始为空，通过 loadEvaluationData 加载）
const evaluationData = ref<EvaluationData>({
    department: '',
    name: '',
    reviewDate: undefined,
    scores: [],
    comment: '',
    additionalScore: undefined
});




// 数据加载方法
const loadEvaluationData = async () => {
    // 分享链接查看模式：允许任何人查看，不需要权限检查
    if (pageMode.value === 'view') {
        // 查看模式下，即使没有权限也允许查看

    } else {
        // 编辑/创建模式：检查用户权限
        // 注意：同事评价者和主管可能没有系统级的绩效评价权限，
        // 但可以对特定的evaluation进行评价
        const userRole = getEvaluationRole();

        // 只有在没有任何角色且没有权限的情况下才拒绝
        if (userRole === 'unknown' && !hasEvaluationPermission()) {
            message.error('您没有绩效评价权限');
            // 没有权限时，重定向到登录页面而不是首页
            router.push('/login');
            return;
        }

        if (!currentUser) {
            message.error('用户信息获取失败，请先登录');
            router.push('/login');
            return;
        }
    }

    if (!evaluationId.value) {
        // 新增模式：使用默认数据
        evaluationData.value = {
            department: '',
            name: currentUser,
            reviewDate: Date.now(),
            colleagueName: '',
            managerName: '',
            scores: [],
            comment: '',
            attachments: undefined,
            additionalScore: undefined
        };
        return;
    }

    loading.value = true;
    try {
        // 根据用户角色选择合适的API
        const userRole = getEvaluationRole();
        const allRoles = getUserAllRoles();
        const hasEmployeeRole = allRoles.includes('employee');
        const hasManagerRole = allRoles.includes('manager');
        const hasAdminRole = allRoles.includes('admin');

        let response;

        console.log('🔍 根据角色选择API:', {
            userRole,
            allRoles,
            hasEmployeeRole,
            hasManagerRole,
            hasAdminRole,
            evaluationId: evaluationId.value,
            currentUser
        });

        if (hasAdminRole) {
            // 管理员使用common API获取基本信息
            response = await fyfcReviewApi.common.getEvaluationById(parseInt(evaluationId.value));
        } else if (hasManagerRole && hasEmployeeRole) {
            // 同时拥有管理者和员工权限的用户
            // 优先尝试使用staff API（因为可能是自己的评价或同事评价）
            try {
                response = await fyfcReviewApi.staff.getEvaluationDetail(parseInt(evaluationId.value), currentUser);
            } catch (error) {
                console.log('staff API失败，尝试manager API:', error);
                // 如果staff API失败，尝试manager API
                response = await fyfcReviewApi.manager.getEvaluationDetail(parseInt(evaluationId.value), currentUser);
            }
        } else if (hasManagerRole) {
            // 仅有管理者权限，使用manager API
            response = await fyfcReviewApi.manager.getEvaluationDetail(parseInt(evaluationId.value), currentUser);
        } else {
            // 员工和其他角色使用staff API
            response = await fyfcReviewApi.staff.getEvaluationDetail(parseInt(evaluationId.value), currentUser);
        }
        if (response.success) {
            console.log('API响应数据:', response);
            console.log('附件数据:', response.data.attachments);

            // 转换API数据格式到组件需要的格式
            evaluationData.value = {
                id: response.data.id,
                department: response.data.department,
                name: response.data.name,
                reviewDate: response.data.reviewDate,
                colleagueName: response.data.colleagueName,
                managerName: response.data.managerName,
                scores: response.data.scores || [],
                comment: response.data.comment || '',
                attachments: response.data.attachments ? JSON.stringify(response.data.attachments) : undefined, // 新增：附件字段（转换为JSON字符串）
                additionalScore: response.data.additionalScore,
                status: response.data.status,
                createdAt: response.data.createdAt,
                createdBy: response.data.createdBy,
                updatedBy: response.data.updatedBy
            };



            message.success('评价数据加载成功');
        } else {
            console.error('API返回错误:', response.message);
            message.error(response.message || '评价数据加载失败');
            router.push('/fyfc/review/staff/dashboard');
        }
    } catch (error) {
        console.error('加载评价数据失败:', error);
        message.error('网络错误，请稍后重试');
        router.push('/fyfc/review/staff/dashboard');
    } finally {
        loading.value = false;
    }
};

// 事件处理器
const handleSave = async (data: EvaluationData) => {

    // 查看模式下不允许保存
    if (pageMode.value === 'view') {
        message.error('查看模式下不允许保存');
        return;
    }

    // 检查用户权限
    // 注意：同事评价者和主管可能没有系统级的绩效评价权限，
    // 但可以对特定的evaluation进行评价，所以这里只检查admin角色的特殊权限
    const userRole = getEvaluationRole();

    // 只有在没有任何角色且没有权限的情况下才拒绝
    if (userRole === 'unknown' && !hasEvaluationPermission()) {
        message.error('您没有绩效评价权限');
        return;
    }

    if (!currentUser) {
        message.error('用户信息获取失败，请先登录');
        return;
    }

    // admin角色检查：不能修改已完成的评价
    if (userRole === 'admin' && data.status === 'completed') {
        message.error('已完成的评价不能修改');
        return;
    }

    loading.value = true;
    try {
        if (evaluationId.value) {
            // 更新现有评价
            const updateDto: EvaluationUpdateDto = {
                id: parseInt(evaluationId.value),
                department: data.department,
                name: data.name,
                reviewDate: data.reviewDate,
                colleagueName: data.colleagueName,
                managerName: data.managerName,
                additionalScore: data.additionalScore,
                score: data.score,
                comment: data.comment,
                attachments: data.attachments, // 新增：附件字段
                updateReason: '用户编辑',
                sendNotification: false
            };

            const response = await fyfcReviewApi.staff.updateEvaluation(updateDto, currentUsername);

            if (response.success) {
                message.success('评价数据更新成功');
                // 可以选择留在当前页面或跳转到列表页面
                // router.push('/fyfc/review/staff/dashboard');
            } else {
                message.error(response.message || '评价数据更新失败');
            }
        } else {
            // 创建新评价
            if (!data.department || !data.name) {
                message.error('部门和姓名为必填项');
                return;
            }

            const formDto: EvaluationFormDto = {
                department: data.department,
                name: data.name,
                reviewDate: data.reviewDate || Date.now(),
                colleagueName: data.colleagueName,
                managerName: data.managerName,
                additionalScore: data.additionalScore,
                comment: data.comment || '',
                attachments: data.attachments // 新增：附件字段
            };

            const response = await fyfcReviewApi.staff.createEvaluation(formDto, currentUsername);

            if (response.success && response.data) {


                // 更新当前页面的评价数据，包含新的ID
                evaluationData.value = {
                    ...evaluationData.value,
                    id: response.data.id,
                    createdAt: response.data.createdAt,
                    createdBy: response.data.createdBy,
                    updatedBy: response.data.updatedBy
                };

                message.success('基础信息保存成功，现在可以进行评分了');

                // 不跳转，留在当前页面继续操作
            } else {
                message.error(response.message || '评价数据创建失败');
            }
        }
    } catch (error) {
        console.error('保存评价数据失败:', error);
        message.error('保存评价数据失败');
    } finally {
        loading.value = false;
    }
};

const handleReset = () => {
    if (evaluationId.value) {
        // 编辑模式：重新加载数据
        loadEvaluationData();
    } else {
        // 新增模式：重置为空
        evaluationData.value = {
            department: '',
            name: currentUser || '',
            reviewDate: Date.now(),
            colleagueName: '',
            managerName: '',
            scores: [],
            comment: '',
            attachments: undefined, // 新增：附件字段
            additionalScore: undefined
        };
    }
};

const handlePreview = (_data: EvaluationData) => {
    // 这里可以打开预览窗口或跳转到预览页面
};

const handleBack = (userRole?: string) => {
    // 如果是分享链接查看模式且用户没有权限，返回登录页面
    if (pageMode.value === 'view' && !hasEvaluationPermission()) {
        router.push('/login');
        return;
    }

    // 根据用户角色返回到对应的dashboard
    switch (userRole) {
        case 'employee':
            router.push('/fyfc/review/staff/dashboard');
            break;
        case 'manager':
            router.push('/fyfc/review/manager/dashboard');
            break;
        case 'admin':
            router.push('/fyfc/review/admin/dashboard');
            break;
        default:
            // 默认返回员工dashboard，如果没有权限则返回登录页面
            if (hasEvaluationPermission()) {
                router.push('/fyfc/review/staff/dashboard');
            } else {
                router.push('/login');
            }
            break;
    }
};

const handleScoreSubmitted = (_evaluationId: number, _scoreType: UserRole) => {
    message.success('评分提交成功');

    // 重新加载评价数据以获取最新状态
    loadEvaluationData();
};

// 监听路由参数变化
watch(() => route.query, () => {
    // 重新解析参数
    const newParams = parseRouteParams();
    evaluationId.value = newParams.id;
    pageMode.value = evaluationId.value ? newParams.mode : 'create';

    // 重新加载数据
    loadEvaluationData();
}, { immediate: false });

// 生命周期
onMounted(() => {
    loadEvaluationData();
});
</script>
<style scoped>
/* 最外层包装器 - 重置所有样式 */
.page-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    margin: 0 !important;
    padding: 0 !important;
    background-color: #f5f5f5;
    overflow-y: auto;
}

/* 容器样式 - 垂直布局居中 */
.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 80px;
    box-sizing: border-box;
}

/* 临时角色切换器样式 */
.temp-role-switcher {
    width: 800px;
    max-width: 90vw;
    margin-bottom: 20px;
    padding: 12px 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px dashed #2196f3;
    border-radius: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
}

/* 内容区域样式 */
.content {
    width: 800px;
    max-width: 90vw;
    background-color: transparent;
    text-align: center; /* 确保内容居中 */
}

/* 分割线样式 */
:deep(.n-divider) {
    width: 100%;
    margin: 0 0 24px 0;
}

/* 卡片样式 */
.n-card {
    width: 100%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: white;
    text-align: left; /* 卡片内容左对齐 */
}

/* 卡片标题居中 */
:deep(.n-card .n-card-header) {
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding-top: 40px;
        width: 100vw;
    }

    .content {
        width: 95vw;
    }
}
</style>