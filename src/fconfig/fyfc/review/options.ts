/**
 * FYFC Review 系统选项配置
 * 提供表单选项、筛选选项等静态配置数据
 */

import type { EvaluationStatus } from './index';
import { getStatusLabel } from './index';

// 状态选项（用于筛选器和表单）
export const statusOptions = [
    { label: getStatusLabel('self'), value: 'self' },
    { label: get<PERSON>tatusLabel('colleague'), value: 'colleague' },
    { label: getStatusLabel('manager'), value: 'manager' },
    { label: getStatusLabel('completed'), value: 'completed' }
];

// 部门选项（示例数据，实际应该从API获取）
export const departmentOptions = [
    { label: '技术部', value: '技术部' },
    { label: '销售部', value: '销售部' },
    { label: '市场部', value: '市场部' },
    { label: '人事部', value: '人事部' },
    { label: '财务部', value: '财务部' },
    { label: '行政部', value: '行政部' }
];

// 评分范围配置
export const scoreRanges = {
    performance: { min: 0, max: 60, label: '绩效评分' },
    attitude: { min: 0, max: 10, label: '态度评分' },
    ability: { min: 0, max: 10, label: '能力评分' },
    growth: { min: 0, max: 10, label: '成长评分' },
    additional: { min: 0, max: 10, label: '线上转发' }
};

// 分页选项
export const paginationOptions = {
    pageSizes: [10, 20, 50, 100],
    defaultPageSize: 10,
    showSizePicker: true,
    showQuickJumper: true
};

// 导出所有选项
export const evaluationOptions = {
    statusOptions,
    departmentOptions,
    scoreRanges,
    paginationOptions
};

export default evaluationOptions;
