import { createRouter, createWebHistory, type RouteRecordRaw } from 'vue-router';
import VueCookies from 'vue-cookies';
import { useUserContext } from '../utils/UserContext';

// 类型定义
declare module 'vue-router' {
  interface RouteMeta {
    title?: string;
    requireAuth?: boolean;
    keepHistory?: boolean;
    keepAlive?: boolean;
    requireRole?: 'admin' | 'manager' | 'employee'; // 添加角色权限要求
  }
}

// 常量定义
const DEFAULT_TITLE = '默认';
const DEFAULT_META = {
  requireAuth: true,
  keepHistory: true,
} as const;

const DEFAULT_META_NoAuth = {
  requireAuth: false,
  keepHistory: true,
} as const;

// 懒加载组件
const Login = () => import('../views/base/login.vue');
const ProseMirrorDemo = () => import('../views/proseMirror/index.vue');

// FYJS 模块
const FyjsProjectManagementIndex = () => import('../views/fyjs/project/index.vue');

// FYG Accounting 模块 - 按功能分组
const FygAccountingDailyReport = () => import(
  /* webpackChunkName: "fyg-reports" */
  '../views/fyg/accounting/dailyReport.vue'
);
const FygAccountingCashFlow = () => import(
  /* webpackChunkName: "fyg-reports" */
  '../views/fyg/accounting/cashFlow.vue'
);
const FygAccountingDailyReportSummary = () => import(
  /* webpackChunkName: "fyg-reports" */
  '../views/fyg/accounting/dailyReportSummary.vue'
);

const FygAccountingProjectEdit = () => import(
  /* webpackChunkName: "fyg-projects" */
  '../views/fyg/accounting/projectEditor.vue'
);
const FygAccountingProjectList = () => import(
  /* webpackChunkName: "fyg-projects" */
  '../views/fyg/accounting/projectList.vue'
);

const FygAccountingCounterpartyEdit = () => import(
  /* webpackChunkName: "fyg-counterparty" */
  '../views/fyg/accounting/counterpartyAccountEditor.vue'
);
const FygAccountingCounterpartyList = () => import(
  /* webpackChunkName: "fyg-counterparty" */
  '../views/fyg/accounting/counterpartyAccountListWithEdit.vue'
);
const FygAccountingCounterpartyTypeList = () => import(
  /* webpackChunkName: "fyg-counterparty" */
  '../views/fyg/accounting/counterpartyAccountProjectTypeList.vue'
);
const FygAccountingEquityDistributionList = () => import(
  /* webpackChunkName: "fyg-counterparty" */
  '../views/fyg/accounting/equityDistributionList.vue'
);

const FygAccountingBankAccountList = () => import(
  /* webpackChunkName: "fyg-bank" */
  '../views/fyg/accounting/bankAccountList.vue'
);
const FygAccountingDataSourceEdit = () => import(
  /* webpackChunkName: "fyg-datasource" */
  '../views/fyg/accounting/dataSourceEditor.vue'
);
const FygAccountingDataSourceList = () => import(
  /* webpackChunkName: "fyg-datasource" */
  '../views/fyg/accounting/dataSourceListWithEdit.vue'
);

// FYFC 模块 - 使用 webpackChunkName 进行命名分块
const FyfcAdminDashboard = () => import(
  /* webpackChunkName: "fyfc-admin" */
  '../views/fyfc/review/admin/dashboard/index.vue'
);
const FyfcManagerDashboard = () => import(
  /* webpackChunkName: "fyfc-manager" */
  '../views/fyfc/review/manager/dashboard/index.vue'
);
const FyfcEmployeeReviewHistory = () => import(
  /* webpackChunkName: "fyfc-employee" */
  '../views/fyfc/review/staff/dashboard/index.vue'
);
const FyfcReviewStaffEdit = () => import(
  /* webpackChunkName: "fyfc-edit" */
  '../views/fyfc/review/staff/edit/index.vue'
);

// 路由配置
const routes: RouteRecordRaw[] = [
  // Game
  {
    path: '/game',
    redirect: '/game.html'
  },

  // 登录页面 - 不需要认证
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: { title: '登录', requireAuth: false }
  },

  // ProseMirror 编辑器
  {
    path: '/prosemirror',
    name: 'ProseMirrorDemo',
    component: ProseMirrorDemo,
    meta: { title: 'ProseMirror编辑器', ...DEFAULT_META }
  },

  // FYJS 模块
  {
    path: '/fyjs',
    children: [{
      path: 'project',
      children: [{
        path: 'index',
        name: 'FyjsProjectManagementIndex',
        component: FyjsProjectManagementIndex,
        meta: { title: '建设工程项目', ...DEFAULT_META }
      }]
    }]
  },

  // FYFC 模块
  {
    path: '/fyfc',
    children: [{
      path: 'review',
      children: [{
        path: 'staff/edit',
        name: 'FyfcReviewStaffEdit',
        component: FyfcReviewStaffEdit,
        meta: { title: '方远房地产集团员工月度绩效考核评分表编辑', ...DEFAULT_META }
      }, {
        path: 'staff/dashboard',
        name: 'FyfcEmployeeReviewHistory',
        component: FyfcEmployeeReviewHistory,
        meta: { title: '员工绩效考核评分表历史记录', ...DEFAULT_META }
      }, {
        path: 'admin/dashboard',
        name: 'FyfcAdminDashboard',
        component: FyfcAdminDashboard,
        meta: { title: '绩效考核评分表管理', requireRole: 'admin', ...DEFAULT_META }
      }, {
        path: 'manager/dashboard',
        name: 'FyfcManagerDashboard',
        component: FyfcManagerDashboard,
        meta: { title: '绩效考核评分表管理', requireRole: 'manager', ...DEFAULT_META }
      }]
    }]
  },

  // FYG 模块
  {
    path: '/fyg',
    children: [{
      path: 'accounting',
      children: [
        // 报表相关
        {
          path: 'summary',
          name: 'FygAccountingDailyReportSummary',
          component: FygAccountingDailyReportSummary,
          meta: { title: '报表汇总', ...DEFAULT_META }
        },
        {
          path: 'daily/:id/:name',
          name: 'FygAccountingDailyReport',
          component: FygAccountingDailyReport,
          meta: { title: '会计日常报表', ...DEFAULT_META }
        },
        {
          path: 'cashflow/:id/:name',
          name: 'FygAccountingCashFlow',
          component: FygAccountingCashFlow,
          meta: { title: '现金流量表', ...DEFAULT_META }
        },

        // 项目管理
        {
          path: 'project',
          children: [{
            path: 'list',
            name: 'FygAccountingProjectList',
            component: FygAccountingProjectList,
            meta: { title: '项目列表', keepAlive: true, ...DEFAULT_META }
          }, {
            path: 'edit/:id',
            name: 'FygAccountingProjectEdit',
            component: FygAccountingProjectEdit,
            meta: { title: '项目编辑', ...DEFAULT_META }
          }]
        },

        // 往来单位管理
        {
          path: 'counterparty',
          children: [{
            path: 'equity/:id/:name',
            name: 'FygAccountingEquityDistributionList',
            component: FygAccountingEquityDistributionList,
            meta: { title: '股权分配列表', ...DEFAULT_META }
          }, {
            path: 'types/:id',
            name: 'FygAccountingCounterpartyTypeList',
            component: FygAccountingCounterpartyTypeList,
            meta: { title: '往来单位类型列表', ...DEFAULT_META }
          }, {
            path: 'list',
            name: 'FygAccountingCounterpartyList',
            component: FygAccountingCounterpartyList,
            meta: { title: '往来单位列表', ...DEFAULT_META }
          }, {
            path: 'edit/:id',
            name: 'FygAccountingCounterpartyEdit',
            component: FygAccountingCounterpartyEdit,
            meta: { title: '往来单位编辑', ...DEFAULT_META }
          }]
        },

        // 银行账户
        {
          path: 'bank/:id/:name',
          name: 'FygAccountingBankAccountList',
          component: FygAccountingBankAccountList,
          meta: { title: '银行账户列表', ...DEFAULT_META }
        },

        // 数据源管理
        {
          path: 'datasource',
          children: [{
            path: 'edit/:id',
            name: 'FygAccountingDataSourceEdit',
            component: FygAccountingDataSourceEdit,
            meta: { title: '数据源编辑', ...DEFAULT_META }
          }, {
            path: 'list/:id/:name',
            name: 'FygAccountingDataSourceList',
            component: FygAccountingDataSourceList,
            meta: { title: '数据源列表', ...DEFAULT_META }
          }]
        }
      ]
    }]
  },

  // 默认重定向
  { path: '/', redirect: '/fyfc/review/staff/dashboard' }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory('/vfyg/'),
  routes,
});

// 类型安全的 cookies 实例
const vueCookies = VueCookies as {
  get: (key: string) => string | null;
  set: (key: string, value: string) => void;
};

// 认证检查函数
const checkAuth = (): boolean => {
  const token = vueCookies.get('x-token');
  return !!token;
};



// 角色检查函数
const checkRole = (requiredRole: 'admin' | 'manager' | 'employee'): boolean => {
  const { getEvaluationRole, user, hasEvaluationPermission, getUserAllRoles, hasAnyRole } = useUserContext();
  const userRole = getEvaluationRole();
  const allRoles = getUserAllRoles();

  console.log('详细角色检查:', {
    requiredRole,
    userRole,
    allRoles,
    hasUser: !!user.value,
    userInfo: user.value,
    hasEvaluationPermission: hasEvaluationPermission()
  });

  // 如果角色为unknown，但用户有评价权限，临时允许访问
  // 这是一个fallback机制，避免因为权限API问题导致的访问失败
  if (userRole === 'unknown' && hasEvaluationPermission()) {
    console.warn('用户角色为unknown但有评价权限，临时允许访问');
    return true;
  }

  // 如果要求admin角色，只有拥有admin权限的用户可以访问
  if (requiredRole === 'admin') {
    return allRoles.includes('admin');
  }

  // 如果要求manager角色，拥有admin或manager权限的用户都可以访问
  if (requiredRole === 'manager') {
    return hasAnyRole(['admin', 'manager']);
  }

  // 如果要求employee角色，拥有任意角色权限的用户都可以访问
  if (requiredRole === 'employee') {
    return hasAnyRole(['admin', 'manager', 'employee']);
  }

  return false;
};

// 路由守卫
router.beforeEach(async (to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title || DEFAULT_TITLE;

  // 保存历史路径
  if (to.meta.keepHistory) {
    vueCookies.set('historyPath', to.fullPath);
  }

  // 认证检查
  if (to.meta.requireAuth) {
    if (!checkAuth()) {
      next({ name: 'login' });
      return;
    }

    // 角色权限检查
    if (to.meta.requireRole) {
      console.log('需要角色权限检查，开始初始化UserContext...');

      // 关键：在路由守卫中直接初始化UserContext
      // 确保用户信息在权限检查前就已经恢复
      const { restoreUser, initializeUser } = useUserContext();

      // 尝试从localStorage恢复用户信息
      const restored = restoreUser();

      // 如果没有恢复成功，从Cookie和权限接口初始化
      if (!restored) {
        console.log('localStorage恢复失败，从API初始化用户信息');
        await initializeUser();
      } else {
        console.log('用户信息已从localStorage恢复');
        // 可选：重新获取最新权限信息
        // await initializeUser();
      }

      // 现在进行权限检查
      if (!checkRole(to.meta.requireRole)) {
        console.warn('用户角色权限不足:', {
          requiredRole: to.meta.requireRole,
          currentPath: to.path
        });
        // 权限不足，重定向到员工dashboard
        next({ path: '/fyfc/review/staff/dashboard' });
        return;
      }
    }

    next();
  } else {
    next();
  }
});

export default router;