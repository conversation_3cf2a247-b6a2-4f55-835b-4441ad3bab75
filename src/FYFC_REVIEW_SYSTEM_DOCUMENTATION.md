# FYFC 绩效评价系统 - 完整文档

## 📋 **系统概述**

FYFC绩效评价系统是一个基于Vue 3 + TypeScript的企业级绩效管理平台，支持多角色评价流程。

### **核心功能**
- **员工自评** - 员工对自己的绩效进行评价
- **同事评价** - 同事之间的横向评价
- **主管评分** - 主管对下属的评价
- **管理员管理** - 系统管理和数据监控

### **技术栈**
- **前端**: Vue 3 + TypeScript + Naive UI
- **状态管理**: Composition API + Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **构建工具**: Vite

## 🏗️ **系统架构**

### **目录结构**
```
src/
├── components/fyfc/review/          # 评价组件
│   ├── EvaluationEditorNew.vue     # 主评价编辑器
│   ├── sections/                   # 功能区块组件
│   │   ├── BasicInfoSection.vue    # 基础信息
│   │   ├── ScoringSection.vue      # 评分区块
│   │   ├── ActionSection.vue       # 操作按钮
│   │   └── FinalScoreSection.vue   # 最终得分
├── views/fyfc/review/              # 页面视图
│   ├── admin/dashboard/            # 管理员仪表板
│   ├── manager/dashboard/          # 主管仪表板
│   └── staff/                      # 员工相关页面
│       ├── dashboard/              # 员工仪表板
│       └── edit/                   # 编辑页面
├── utils/                          # 工具类
│   ├── UserContext.ts             # 用户上下文管理
│   ├── FyfcReviewApi.ts           # API接口定义
│   └── crypto.ts                  # 加密工具
└── router/                         # 路由配置
    └── index.ts                   # 路由守卫和权限控制
```

### **权限控制架构**
```
1. 路由层面 (router/index.ts)
   ├── 基础认证检查 (requireAuth)
   ├── 角色权限检查 (requireRole)
   └── UserContext初始化

2. 页面层面 (views/*/index.vue)
   ├── 用户权限验证
   ├── 数据加载权限
   └── 功能访问控制

3. 组件层面 (components/*/sections/)
   ├── 角色计算 (currentUserRole)
   ├── 功能权限控制 (canEdit, canView)
   └── 按钮显示控制
```

## 👥 **角色权限设计**

### **角色定义**
| 角色 | 权限代码 | 主要功能 |
|------|----------|----------|
| **员工** | `fyfc-evaluation-employee` | 自评、查看自己的评价历史 |
| **主管** | `fyfc-evaluation-manager` | 主管评分、查看下属评价 |
| **管理员** | `fyfc-evaluation-admin` | 系统管理、查看所有数据 |
| **同事** | 无系统权限 | 通过分享链接进行同事评价 |

### **API权限映射**
| 角色 | API端点 | 权限范围 |
|------|---------|----------|
| **员工** | `/staff/*` | 只能访问自己相关的evaluation |
| **主管** | `/manager/*` | 只能访问下属的evaluation |
| **管理员** | `/admin/*`, `/manager/*` | 可以访问所有evaluation |
| **通用** | `/common/*` | 基础信息，权限较宽松 |

## 🔧 **核心功能实现**

### **1. 用户认证与权限管理**

#### **UserContext系统**
```typescript
// 用户信息管理
interface UserInfo {
  username: string;
  name: string;
  role: 'admin' | 'manager' | 'employee' | 'unknown';
  permissions: string[];
}

// 权限检查
const hasEvaluationPermission = (): boolean => {
  return permissions.includes('fyfc-evaluation-*');
};

// 角色判断
const getEvaluationRole = (): UserRole => {
  // 根据权限代码确定角色
};
```

#### **路由守卫**
```typescript
router.beforeEach(async (to, from, next) => {
  // 1. 认证检查
  if (to.meta.requireAuth && !checkAuth()) {
    next('/login');
    return;
  }

  // 2. 角色权限检查
  if (to.meta.requireRole) {
    await initUserContext(); // 确保UserContext初始化
    if (!checkRole(to.meta.requireRole)) {
      next('/fyfc/review/staff/dashboard');
      return;
    }
  }

  next();
});
```

### **2. 评价数据管理**

#### **数据结构**
```typescript
interface EvaluationData {
  id?: number;
  department: string;
  name: string;
  reviewDate: number;
  colleagueName?: string;
  managerName?: string;
  scores: ScoreData[];
  comment?: string;
  additionalScore?: number;
  status?: string;
}

interface ScoreData {
  type: 'employee' | 'colleague' | 'manager';
  performanceScore: number;
  attitudeScore: number;
  abilityScore: number;
  growthScore: number;
  evaluationScore: number;
  evaluator: string;
}
```

#### **API调用策略**
```typescript
// 根据角色选择合适的API
const loadEvaluationData = async () => {
  const userRole = getEvaluationRole();
  let response;

  if (userRole === 'admin') {
    try {
      response = await fyfcReviewApi.admin.getEvaluationDetail(id, currentUser);
    } catch {
      response = await fyfcReviewApi.manager.getEvaluationDetail(id, currentUser);
    }
  } else if (userRole === 'manager') {
    response = await fyfcReviewApi.manager.getEvaluationDetail(id, currentUser);
  } else {
    response = await fyfcReviewApi.staff.getEvaluationDetail(id, currentUser);
  }
};
```

### **3. 评分系统**

#### **评分约束**
- **绩效得分**: 0-60分
- **工作态度**: 0-10分
- **工作能力**: 0-10分
- **个人成长**: 0-10分
- **附加分**: 0-10分（仅管理员可编辑）

#### **最终得分计算**
```typescript
const calculateFinalScore = (scores: ScoreData[], additionalScore: number) => {
  const selfScore = scores.find(s => s.type === 'employee')?.evaluationScore || 0;
  const colleagueScore = scores.find(s => s.type === 'colleague')?.evaluationScore || 0;
  const managerScore = scores.find(s => s.type === 'manager')?.evaluationScore || 0;

  // 如果没有同事评价，主管评价权重从70%调整为90%
  if (colleagueScore === 0) {
    return selfScore * 0.1 + managerScore * 0.9 + additionalScore;
  } else {
    return selfScore * 0.1 + colleagueScore * 0.2 + managerScore * 0.7 + additionalScore;
  }
};
```

### **4. 分享链接系统**

#### **链接加密**
```typescript
const generateEncryptedUrl = (path: string, params: object) => {
  const encryptedParams = encrypt(JSON.stringify(params));
  return `${baseUrl}${path}?data=${encryptedParams}`;
};
```

#### **权限验证**
```typescript
const validateShareAccess = (evaluationData: EvaluationData, currentUser: string) => {
  return evaluationData.name === currentUser ||
         evaluationData.colleagueName === currentUser ||
         evaluationData.managerName === currentUser;
};
```

## 🚀 **部署与配置**

### **环境变量**
```bash
# API基础URL
VITE_API_BASE_URL=/fyschedule/api/fyfc/evaluation

# 加密密钥
VITE_CRYPTO_SECRET_KEY=your-secret-key
```

### **构建命令**
```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 类型检查
npm run type-check
```

## 📝 **开发指南**

### **添加新角色**
1. 在`UserContext.ts`中添加权限代码
2. 在`router/index.ts`中添加路由权限
3. 在组件中添加角色判断逻辑
4. 在API中添加相应的端点

### **添加新功能**
1. 在`sections/`目录下创建新的功能组件
2. 在`EvaluationEditorNew.vue`中集成组件
3. 添加相应的API接口
4. 更新权限控制逻辑

### **调试技巧**
```typescript
// 启用详细日志
console.log('权限检查:', {
  userRole: getEvaluationRole(),
  hasPermission: hasEvaluationPermission(),
  userInfo: user.value
});
```

## 🔍 **故障排除**

### **常见问题**

#### **1. 用户无法访问页面**
- 检查UserContext是否正确初始化
- 验证权限代码是否匹配
- 确认路由守卫配置

#### **2. 数据加载失败**
- 检查API端点权限
- 验证用户角色判断
- 确认后端权限配置

#### **3. 评分功能异常**
- 检查评分约束配置
- 验证数据格式
- 确认API调用参数

### **调试工具**
- 浏览器开发者工具
- Vue DevTools
- Network面板查看API调用
- Console查看权限日志

## 📊 **性能优化**

### **已实现的优化**
- 组件懒加载
- API响应缓存
- 权限信息本地存储
- 路由级别的代码分割

### **建议的优化**
- 实现虚拟滚动（大数据列表）
- 添加Service Worker（离线支持）
- 优化图片资源（WebP格式）
- 实现增量更新

## 🔒 **安全考虑**

### **已实现的安全措施**
- 分享链接参数加密
- 多层权限验证
- API调用权限检查
- 用户会话管理

### **安全建议**
- 定期更新依赖包
- 实施CSP策略
- 添加API限流
- 实现审计日志

---

**最后更新**: 2024年12月
**维护者**: 开发团队
**版本**: 1.0.0
