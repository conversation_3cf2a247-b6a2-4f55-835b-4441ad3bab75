<template>
    <div class="logout-container">
        <!-- 会话信息显示 -->
        <div v-if="showSessionInfo && isLoggedIn" class="session-info">
            <n-text depth="3" style="font-size: 12px;">
                用户: {{ displayName }} |
                剩余时间: {{ formatRemainingTime(remainingTime) }}
            </n-text>
        </div>

        <!-- 注销按钮 -->
        <n-button
            v-if="isLoggedIn"
            type="error"
            size="small"
            @click="handleLogout"
            :loading="isLoggingOut"
            class="logout-button"
        >
            <template #icon>
                <n-icon>
                    <LogOutOutline />
                </n-icon>
            </template>
            注销
        </n-button>

        <!-- 会话过期警告对话框 -->
        <n-modal
            v-model:show="showExpirationWarning"
            preset="dialog"
            title="会话即将过期"
            content="您的会话将在5分钟内过期，是否继续保持登录状态？"
            positive-text="继续登录"
            negative-text="立即注销"
            @positive-click="handleExtendSession"
            @negative-click="handleLogout"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { NButton, NText, NIcon, NModal, useMessage } from 'naive-ui';
import { LogOutOutline } from '@vicons/ionicons5';
import { useUserContext } from '../../utils/UserContext';

// Props 定义
interface Props {
    showSessionInfo?: boolean; // 是否显示会话信息
    autoWarning?: boolean; // 是否自动显示过期警告
}

const props = withDefaults(defineProps<Props>(), {
    showSessionInfo: false,
    autoWarning: true
});

const message = useMessage();
const {
    isLoggedIn,
    displayName,
    logout,
    updateActivity,
    getSessionRemainingTime
} = useUserContext();

// 组件状态
const isLoggingOut = ref(false);
const showExpirationWarning = ref(false);
const remainingTime = ref(0);

// 定时器
let sessionTimer: NodeJS.Timeout | null = null;
let warningTimer: NodeJS.Timeout | null = null;

// 更新剩余时间
const updateRemainingTime = () => {
    remainingTime.value = getSessionRemainingTime();

    // 如果剩余时间少于5分钟且启用了自动警告，显示警告
    if (props.autoWarning && remainingTime.value > 0 && remainingTime.value < 5 * 60 * 1000) {
        if (!showExpirationWarning.value) {
            showExpirationWarning.value = true;
        }
    }
};

// 格式化剩余时间
const formatRemainingTime = (time: number): string => {
    if (time <= 0) return '已过期';

    const hours = Math.floor(time / (60 * 60 * 1000));
    const minutes = Math.floor((time % (60 * 60 * 1000)) / (60 * 1000));

    if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
    } else {
        return `${minutes}分钟`;
    }
};

// 处理注销
const handleLogout = async () => {
    try {
        isLoggingOut.value = true;

        // 简单确认注销
        if (confirm('确定要注销当前账户吗？')) {
            logout();
            // logout函数会自动跳转到登录页面，不需要额外的成功提示
        }
    } catch (error) {
        console.error('注销失败:', error);
        message.error('注销失败，请重试');
    } finally {
        isLoggingOut.value = false;
        showExpirationWarning.value = false;
    }
};

// 处理延长会话
const handleExtendSession = () => {
    updateActivity();
    showExpirationWarning.value = false;
    message.success('会话已延长');
};

// 启动定时器
const startTimers = () => {
    // 每30秒更新一次剩余时间
    sessionTimer = setInterval(() => {
        if (isLoggedIn.value) {
            updateRemainingTime();
        } else {
            stopTimers();
        }
    }, 30 * 1000);

    // 立即更新一次
    updateRemainingTime();
};

// 停止定时器
const stopTimers = () => {
    if (sessionTimer) {
        clearInterval(sessionTimer);
        sessionTimer = null;
    }
    if (warningTimer) {
        clearTimeout(warningTimer);
        warningTimer = null;
    }
};

// 生命周期
onMounted(() => {
    if (isLoggedIn.value) {
        startTimers();
    }
});

onUnmounted(() => {
    stopTimers();
});

// 监听登录状态变化
const stopWatcher = computed(() => isLoggedIn.value);
const unwatchLogin = () => {
    if (stopWatcher.value) {
        startTimers();
    } else {
        stopTimers();
    }
};

// 监听登录状态
onMounted(() => {
    unwatchLogin();
});
</script>

<style scoped>
.logout-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.session-info {
    padding: 4px 8px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.logout-button {
    transition: all 0.3s ease;
}

.logout-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
}

/* 移动端优化 */
@media (max-width: 768px) {
    .logout-container {
        flex-direction: column;
        gap: 8px;
    }

    .session-info {
        width: 100%;
        text-align: center;
    }
}
</style>
