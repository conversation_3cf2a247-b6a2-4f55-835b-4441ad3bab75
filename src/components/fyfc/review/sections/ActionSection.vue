<template>
    <!-- 操作按钮区域 -->
    <div class="action-section" v-if="showActions">
        <n-space :justify="isMobile ? 'space-around' : 'center'" :size="isMobile ? 'small' : 'medium'"
            :wrap="isMobile">
            <!-- 返回按钮：除了同事外都可以看到 -->
            <n-button v-if="userRole?.type !== 'colleague'"
                @click="handleBack" :style="{ width: isMobile ? '100px' : 'auto' }">
                返回
            </n-button>
            <!-- 重置按钮：只有员工自己和管理员可以看到 -->
            <n-button v-if="userRole?.type === 'employee' || userRole?.type === 'admin'"
                @click="handleReset" :disabled="!canEdit" :style="{ width: isMobile ? '100px' : 'auto' }">
                重置
            </n-button>
            <!-- 保存按钮：只有员工自己和管理员可以看到 -->
            <n-button v-if="canShowSaveButton" :type="saveButtonType" @click="handleSave" :disabled="!canSave"
                :style="{ width: isMobile ? '120px' : 'auto' }">
                {{ saveButtonText }}
            </n-button>
            <!-- 提交评分按钮 -->
            <n-button v-if="canSubmitScore" type="success" @click="handleSubmitScore"
                :style="{ width: isMobile ? '100px' : 'auto' }">
                提交评分
            </n-button>
            <!-- 分享链接按钮：只有员工自己可以看到 -->
            <n-button v-if="canShowShareButton" type="info" @click="handleShare"
                :style="{ width: isMobile ? '100px' : 'auto' }">
                分享链接
            </n-button>
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NSpace, NButton } from 'naive-ui';
import type { UserRole } from '../../../../fconfig/fyfc/review';
import { useUserContext } from '../../../../utils/UserContext';

// 类型定义
export interface EvaluationScore {
    id?: number;
    evaluationId?: number;
    evaluator?: string;
    type: UserRole;
    performanceScore?: number | null;
    attitudeScore?: number | null;
    abilityScore?: number | null;
    growthScore?: number | null;
    score?: number;
    signature?: string;
}

export interface UserRoleConfig {
    type: UserRole;
    canEdit: boolean;
    canView: boolean;
}

export interface EvaluationData {
    id?: number;
    department?: string;
    name?: string;
    reviewDate?: number;
    colleagueName?: string;
    managerName?: string;
    additionalScore?: number;
    score?: number;
    comment?: string;
    createdAt?: number;
    createdBy?: string;
    updatedBy?: string;
    status?: string;
    scores?: EvaluationScore[];
}

// Props 定义
interface Props {
    formData: EvaluationData;
    userRole?: UserRoleConfig;
    isMobile: boolean;
    canEdit: boolean;
    showActions: boolean;
}

// Emits 定义
interface Emits {
    (e: 'back'): void;
    (e: 'reset'): void;
    (e: 'save'): void;
    (e: 'submitScore'): void;
    (e: 'share'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 是否可以提交评分
const canSubmitScore = computed(() => {
    // 必须有评价ID且有用户角色
    if (!props.userRole || !props.formData.id) return false;

    // 管理员不能提交评分
    if (props.userRole.type === 'admin') return false;

    // 检查是否有当前用户类型的评分数据
    const currentUserScore = props.formData.scores?.find(score => score.type === props.userRole?.type);
    if (!currentUserScore) return false;

    // 检查评分数据是否完整
    const hasValidScore = (currentUserScore.performanceScore || 0) +
                         (currentUserScore.attitudeScore || 0) +
                         (currentUserScore.abilityScore || 0) +
                         (currentUserScore.growthScore || 0) > 0;

    return hasValidScore && props.userRole.canEdit;
});

// 是否显示保存按钮
const canShowSaveButton = computed(() => {
    // 只有员工自己和管理员可以看到保存按钮
    return props.userRole?.type === 'employee' || props.userRole?.type === 'admin';
});

// 是否可以保存（考虑状态和角色）
const canSave = computed(() => {
    // 基础权限检查
    if (!props.canEdit) return false;

    // admin角色：只要不是completed状态都可以保存
    if (props.userRole?.type === 'admin') {
        return props.formData.status !== 'completed';
    }

    // 其他角色：使用原有的canEdit逻辑
    return props.canEdit;
});

// 是否显示分享按钮
const canShowShareButton = computed(() => {
    // 只有员工自己可以看到分享按钮
    if (props.userRole?.type !== 'employee') return false;

    // 必须有评价ID
    if (!props.formData.id) return false;

    // 必须是员工本人
    const { getCurrentDisplayName } = useUserContext();
    const currentUser = getCurrentDisplayName();
    return props.formData.name === currentUser;
});

// 保存按钮文案
const saveButtonText = computed(() => {
    if (props.userRole?.type === 'admin') {
        return '保存评价';
    }

    if (props.userRole?.type === 'employee') {
        return '保存基础信息';
    }

    return '保存';
});

// 保存按钮样式类型
const saveButtonType = computed(() => {
    if (props.userRole?.type === 'admin') {
        return 'warning'; // 管理员用警告色区分
    }

    return 'primary'; // 员工用主色
});

// 事件处理
const handleBack = () => {
    emit('back');
};

const handleReset = () => {
    emit('reset');
};

const handleSave = () => {
    emit('save');
};

const handleSubmitScore = () => {
    emit('submitScore');
};

const handleShare = () => {
    emit('share');
};
</script>

<style scoped>
/* 操作按钮区域 */
.action-section {
    margin-top: 32px;
    padding-top: 20px;
    border-top: 2px solid #f0f0f0;
}

/* 移动端优化 */
@media (max-width: 480px) {
    .action-section {
        margin-top: 24px;
        padding-top: 16px;
    }
}

/* 按钮样式 */
:deep(.n-button) {
    transition: all 0.3s ease;
}

:deep(.n-button:hover) {
    transform: translateY(-1px);
}
</style>
