<template>
    <!-- 评分项目区域 -->
    <div class="scoring-section">
        <div class="section-title">
            <n-text strong>评分项目</n-text>
            <n-text depth="3" style="font-size: 12px; margin-left: 8px">
                (总分90分: 工作业绩60分 + 工作态度10分 + 工作能力10分 + 个人成长10分)
            </n-text>
        </div>

        <!-- 评分类型切换（管理员可见） -->
        <div v-if="userRole?.type === 'admin'" class="score-type-tabs">
            <n-space>
                <n-button
                    v-for="scoreType in availableScoreTypes"
                    :key="scoreType.type"
                    :type="currentScoreType === scoreType.type ? 'primary' : 'default'"
                    size="small"
                    @click="handleScoreTypeChange(scoreType.type)"
                >
                    {{ scoreType.label }}
                </n-button>
            </n-space>
        </div>

        <div class="score-list">
            <div class="score-item-row" v-for="item in scoreItems" :key="item.key">
                <div class="score-label-section">
                    <n-text strong>{{ item.label }}</n-text>
                    <n-text depth="3" style="font-size: 11px; margin-left: 8px">
                        ({{ item.min }}-{{ item.max }}{{ item.unit }})
                    </n-text>
                </div>
                <div class="score-input-section">
                    <n-input-number
                        :value="(currentScore[item.key as keyof EvaluationScore] as number) || null"
                        :min="item.min"
                        :max="item.max"
                        :step="1"
                        :precision="1"
                        :placeholder="`${item.min}-${item.max}`"
                        :disabled="!canEditCurrentScore"
                        :style="{ width: isMobile ? '100%' : '120px' }"
                        class="score-input"
                        @update:value="updateScore(item.key as keyof EvaluationScore, $event)"
                    >
                        <template #suffix>分</template>
                    </n-input-number>
                </div>
            </div>
        </div>

        <!-- 小计区域 -->
        <div class="subtotal-compact">
            <div class="subtotal-row">
                <div class="subtotal-left">
                    <span class="subtotal-label">小计：</span>
                    <n-text strong style="font-size: 16px; color: #2080f0">
                        {{ (subtotal || 0).toFixed(1) }} / {{ maxTotalScore }} 分
                    </n-text>
                </div>
                <div class="subtotal-right" v-if="currentScore.evaluator">
                    <span class="evaluator-label">评分人：</span>
                    <n-text strong style="font-size: 14px; color: #666">
                        {{ currentScore.evaluator }}
                    </n-text>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
    NText,
    NSpace,
    NButton,
    NInputNumber
} from 'naive-ui';
import type { UserRole } from '../../../../fconfig/fyfc/review';

// 类型定义
export interface EvaluationScore {
    id?: number;
    evaluationId?: number;
    evaluator?: string;
    type: UserRole;
    performanceScore?: number | null;
    attitudeScore?: number | null;
    abilityScore?: number | null;
    growthScore?: number | null;
    score?: number;
    signature?: string;
}

export interface UserRoleConfig {
    type: UserRole;
    canEdit: boolean;
    canView: boolean;
}

// Props 定义
interface Props {
    scores?: EvaluationScore[];
    userRole?: UserRoleConfig;
    currentScoreType: UserRole;
    isMobile: boolean;
    canEdit: boolean;
}

// Emits 定义
interface Emits {
    (e: 'updateScore', key: keyof EvaluationScore, value: number | null): void;
    (e: 'scoreTypeChange', type: UserRole): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 评分项目配置
const scoreItems = [
    { key: 'performanceScore', label: '工作业绩', min: 0, max: 60, unit: '分' },
    { key: 'attitudeScore', label: '工作态度', min: 0, max: 10, unit: '分' },
    { key: 'abilityScore', label: '工作能力', min: 0, max: 10, unit: '分' },
    { key: 'growthScore', label: '个人成长', min: 0, max: 10, unit: '分' }
];

// 最大总分
const maxTotalScore = 90;

// 可用的评分类型（仅管理员可见切换按钮）
const availableScoreTypes = computed(() => {
    const scores = props.scores || [];
    const userRole = props.userRole?.type;

    // 所有可能的评分类型
    const allTypes = [
        { type: 'employee' as UserRole, label: '员工自评' },
        { type: 'colleague' as UserRole, label: '同事评价' },
        { type: 'manager' as UserRole, label: '主管审核' }
    ];

    // 只有管理员才显示切换按钮，且只显示有数据的类型
    if (userRole === 'admin') {
        return allTypes.filter(type =>
            scores.some(score => score.type === type.type ||
                (type.type === 'employee'))
        );
    }

    // 非管理员不显示切换按钮
    return [];
});

// 当前评分数据
const currentScore = computed(() => {
    const scores = props.scores || [];
    const userRole = props.userRole?.type;

    // 确定要查找的评分类型
    let scoreType: UserRole;
    if (userRole === 'admin') {
        scoreType = props.currentScoreType;
    } else {
        scoreType = userRole || 'employee';
    }

    // 查找对应的评分数据，支持类型映射
    const foundScore = scores.find(score =>
        score.type === scoreType ||
        (scoreType === 'employee')
    );

    return foundScore || {
        type: scoreType,
        performanceScore: null,
        attitudeScore: null,
        abilityScore: null,
        growthScore: null
    } as EvaluationScore;
});

// 当前评分是否可编辑
const canEditCurrentScore = computed(() => {
    if (props.userRole?.type === 'admin') {
        return false; // 管理员只能查看，不能编辑
    }
    return props.canEdit && props.currentScoreType === props.userRole?.type;
});

// 小计
const subtotal = computed(() => {
    const score = currentScore.value;
    return (score.performanceScore || 0) +
           (score.attitudeScore || 0) +
           (score.abilityScore || 0) +
           (score.growthScore || 0);
});

// 事件处理
const updateScore = (key: keyof EvaluationScore, value: number | null) => {
    emit('updateScore', key, value);
};

const handleScoreTypeChange = (type: UserRole) => {
    emit('scoreTypeChange', type);
};
</script>

<style scoped>
/* 区域标题样式 */
.section-title {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

/* 评分区域 */
.scoring-section {
    margin: 20px 0;
}

.score-list {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.score-item-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #fafafa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.score-item-row:hover {
    background-color: #f5f5f5;
    border-color: #2080f0;
    box-shadow: 0 2px 8px rgba(32, 128, 240, 0.1);
}

.score-label-section {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.score-input-section {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.score-input {
    text-align: center;
}

/* 小计区域 - 紧凑设计 */
.subtotal-compact {
    margin: 16px 0;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.subtotal-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    flex-wrap: wrap;
}

.subtotal-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.subtotal-right {
    display: flex;
    align-items: center;
    gap: 6px;
}

.subtotal-label {
    font-weight: 500;
    color: #666;
    font-size: 14px;
}

.evaluator-label {
    font-weight: 500;
    color: #888;
    font-size: 13px;
}

/* 评分类型切换 */
.score-type-tabs {
    margin: 16px 0 12px 0;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .score-item-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        padding: 12px;
    }

    .score-input-section {
        width: 100%;
    }

    .subtotal-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .subtotal-left,
    .subtotal-right {
        width: 100%;
        justify-content: flex-start;
    }
}

/* 深度样式 */
:deep(.n-input-number .n-input__input-el) {
    text-align: center;
}
</style>
