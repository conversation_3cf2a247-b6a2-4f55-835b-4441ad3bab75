<template>
  <n-card title="附件管理" size="small" :bordered="false">
    <template #header-extra>
      <n-space size="small">
        <n-tag v-if="attachments.length > 0" type="info" size="small">
          {{ attachments.length }} 个文件
        </n-tag>
        <n-tag v-if="!canUpload && evaluationStatus !== 'completed'" type="warning" size="small">
          仅创建人可编辑
        </n-tag>
        <n-tag v-if="evaluationStatus === 'completed'" type="success" size="small">
          已完成（只读）
        </n-tag>
      </n-space>
    </template>

    <!-- 上传区域 -->
    <div v-if="canUpload" class="upload-section">
      <n-upload
        :custom-request="handleCustomRequest"
        :file-list="fileList"
        :max="10"
        multiple
        :show-file-list="false"
        @before-upload="handleBeforeUpload"
        @finish="handleUploadFinish"
        @error="handleUploadError"
        @change="handleUploadChange"
      >
        <n-upload-dragger>
          <div style="margin-bottom: 12px">
            <n-icon size="48" :depth="3">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
              </svg>
            </n-icon>
          </div>
          <n-text style="font-size: 16px">
            点击或者拖动文件到该区域来上传
          </n-text>
          <n-p depth="3" style="margin: 8px 0 0 0">
            支持单个或批量上传，单个文件不超过50MB
          </n-p>
        </n-upload-dragger>
      </n-upload>
    </div>

    <!-- 无权限提示 -->
    <div v-else-if="!canUpload && evaluationId" class="no-permission-info">
      <n-alert type="info" :show-icon="false">
        {{ evaluationStatus === 'completed' ? '评价已完成，无法上传附件' : '只有评价创建人可以上传附件' }}
      </n-alert>
    </div>

    <!-- 附件列表 -->
    <div v-if="attachments.length > 0" class="attachment-list">
      <div v-for="attachment in attachments" :key="attachment.id" class="attachment-item">
        <div class="attachment-info">
          <span class="file-icon">{{ getFileTypeIcon(attachment.fileType) }}</span>
          <div class="file-details">
            <div class="file-name">{{ attachment.fileName }}</div>
            <div class="file-meta">
              <span>{{ formatFileSize(attachment.fileSize) }}</span>
              <span>{{ attachment.uploadBy }}</span>
              <span>{{ formatUploadTime(attachment.uploadTime) }}</span>
            </div>
          </div>
        </div>
        <div class="attachment-actions">
          <n-button size="small" type="primary" ghost @click="handleDownload(attachment)">
            下载
          </n-button>
          <n-button v-if="canPreview(attachment)" size="small" type="info" ghost @click="handlePreview(attachment)">
            预览
          </n-button>
          <n-button v-if="canDelete" size="small" type="error" ghost @click="handleDelete(attachment)">
            删除
          </n-button>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!canUpload" class="empty-state">
      <n-empty description="暂无附件" />
    </div>
  </n-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  NCard, NUpload, NUploadDragger, NIcon, NText, NP, NSpace, NTag, NButton, NEmpty, NAlert,
  useMessage, type UploadCustomRequestOptions, type UploadFileInfo
} from 'naive-ui';

// 导入 fyfcOssService 实现真实的下载和预览功能
import { fyfcOssService } from '../../../../utils/FyfcOssService';

// 本地定义类型，避免外部依赖
interface FyfcAttachment {
  id: string;
  fileName: string;
  fileKey: string;
  bucketName?: string;
  fileSize: number;
  fileType: string;
  uploadTime: number;
  uploadBy: string;
  fileUrl?: string;
}

interface Props {
  evaluationId?: number;
  uploadBy: string;
  evaluationStatus?: string;
  evaluationCreatedBy?: string;
  currentUser: string;
  readonly?: boolean;
  initialAttachments?: string; // 新增：初始附件数据（JSON字符串）
}

interface Emits {
  (e: 'attachments-updated', attachments: FyfcAttachment[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  evaluationStatus: 'self',
  evaluationCreatedBy: '',
  currentUser: '',
  initialAttachments: undefined
});

const emit = defineEmits<Emits>();
const message = useMessage();

// 权限计算 - 简化逻辑
const canUpload = computed(() => {
  if (props.readonly) return false;
  if (props.evaluationStatus === 'completed') return false;
  return props.currentUser === props.evaluationCreatedBy;
});

const canDelete = computed(() => {
  if (props.readonly) return false;
  if (props.evaluationStatus === 'completed') return false;
  return props.currentUser === props.evaluationCreatedBy;
});

// 响应式数据
const attachments = ref<FyfcAttachment[]>([]);
const fileList = ref<UploadFileInfo[]>([]);

// 使用OSS服务的工具函数
const getFileTypeIcon = (fileType: string): string => {
  return fyfcOssService.getFileTypeIcon(fileType);
};

const formatFileSize = (sizeInBytes: number): string => {
  return fyfcOssService.formatFileSize(sizeInBytes);
};

const formatUploadTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN');
};

// 检查是否可以预览 - 使用OSS服务的方法
const canPreview = (attachment: FyfcAttachment) => {
  return fyfcOssService.isImageFile(attachment.fileType) || fyfcOssService.isDocumentFile(attachment.fileType);
};

// 加载附件列表 - 从服务器获取最新数据
const loadAttachments = async () => {
  try {
    if (props.evaluationId) {
      // 从服务器获取最新的附件列表
      const serverAttachments = await fyfcOssService.getEvaluationAttachments(props.evaluationId);
      attachments.value = serverAttachments;
    } else if (props.initialAttachments) {
      // 如果没有evaluationId，则从props解析数据
      const parsedAttachments = JSON.parse(props.initialAttachments) as FyfcAttachment[];
      attachments.value = Array.isArray(parsedAttachments) ? parsedAttachments : [];
    } else {
      // 如果都没有，使用空数组
      attachments.value = [];
    }

    emit('attachments-updated', attachments.value);
  } catch (error) {
    console.error('加载附件失败:', error);
    // 如果服务器请求失败，尝试从props解析数据
    try {
      if (props.initialAttachments) {
        const parsedAttachments = JSON.parse(props.initialAttachments) as FyfcAttachment[];
        attachments.value = Array.isArray(parsedAttachments) ? parsedAttachments : [];
      } else {
        attachments.value = [];
      }
    } catch (parseError) {
      attachments.value = [];
    }
    emit('attachments-updated', attachments.value);
  }
};

// 自定义上传请求 - 真实上传实现
const handleCustomRequest = async (options: UploadCustomRequestOptions) => {
  const { file, onProgress, onFinish, onError } = options;

  console.log('开始自定义上传请求:', {
    fileName: file.name,
    fileSize: file.file?.size,
    evaluationId: props.evaluationId,
    uploadBy: props.uploadBy
  });

  if (!props.evaluationId) {
    console.error('评价ID为空');
    onError();
    message.error('评价ID不能为空');
    return;
  }

  try {
    onProgress({ percent: 10 });
    console.log('开始调用OSS上传服务');

    const attachment = await fyfcOssService.uploadFile(
      file.file as File,
      props.evaluationId,
      props.uploadBy
    );

    console.log('OSS上传结果:', attachment);

    if (attachment) {
      onProgress({ percent: 100 });
      onFinish();
      message.success(`文件 "${file.name}" 上传成功`);

      // 重新加载附件列表
      console.log('重新加载附件列表');
      await loadAttachments();
    } else {
      console.error('上传返回空结果，可能的原因：');
      console.error('1. API返回success=false');
      console.error('2. API返回data=null');
      console.error('3. 网络请求失败');
      console.error('4. 服务器内部错误');
      onError();
      message.error(`文件 "${file.name}" 上传失败，请检查控制台日志`);
    }
  } catch (error) {
    console.error('上传失败:', error);
    onError();
    message.error(`文件 "${file.name}" 上传失败: ${error}`);
  }
};

// 上传前检查
const handleBeforeUpload = (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const { file } = data;

  console.log('开始上传前检查:', {
    fileName: file.name,
    fileSize: file.file?.size,
    canUpload: canUpload.value,
    evaluationId: props.evaluationId,
    currentUser: props.currentUser,
    evaluationCreatedBy: props.evaluationCreatedBy
  });

  // 检查上传权限
  if (!canUpload.value) {
    if (props.evaluationStatus === 'completed') {
      message.warning('评价已完成，无法上传附件');
    } else {
      message.warning('只有评价创建人可以上传附件');
    }
    return false;
  }

  // 检查文件大小（50MB）
  if (file.file && file.file.size > 50 * 1024 * 1024) {
    message.error(`文件 "${file.name}" 大小超过50MB限制`);
    return false;
  }

  console.log('上传前检查通过');
  return true;
};

// 上传变化
const handleUploadChange = (options: { file: UploadFileInfo; fileList: UploadFileInfo[]; event?: Event }) => {
  console.log('上传文件列表变化:', options.fileList.length, '个文件');
  fileList.value = options.fileList;
};

// 上传完成
const handleUploadFinish = () => {
  console.log('上传完成');
  fileList.value = [];
};

// 上传错误
const handleUploadError = () => {
  console.log('上传错误');
  fileList.value = [];
};

// 下载文件
const handleDownload = async (attachment: FyfcAttachment) => {
  try {
    message.info(`开始下载文件 "${attachment.fileName}"`);
    await fyfcOssService.downloadFile(attachment);
    message.success(`文件 "${attachment.fileName}" 下载成功`);
  } catch (error) {
    console.error('下载文件失败:', error);
    message.error(`文件 "${attachment.fileName}" 下载失败`);
  }
};

// 预览文件
const handlePreview = (attachment: FyfcAttachment) => {
  try {
    fyfcOssService.previewFile(attachment.fileKey, attachment.bucketName);
    message.info(`正在预览文件 "${attachment.fileName}"`);
  } catch (error) {
    console.error('预览文件失败:', error);
    message.error(`文件 "${attachment.fileName}" 预览失败`);
  }
};

// 删除文件
const handleDelete = async (attachment: FyfcAttachment) => {
  if (!canDelete.value) {
    if (props.evaluationStatus === 'completed') {
      message.warning('评价已完成，无法删除附件');
    } else {
      message.warning('只有评价创建人可以删除附件');
    }
    return;
  }

  // 使用简单的确认提示，与项目其他地方保持一致
  if (!window.confirm(`确定要删除文件 "${attachment.fileName}" 吗？此操作不可撤销。`)) {
    return;
  }

  try {
    const success = await fyfcOssService.deleteFile(
      attachment.fileKey,
      props.evaluationId!,
      props.currentUser,
      attachment.bucketName
    );

    if (success) {
      message.success(`文件 "${attachment.fileName}" 删除成功`);
      // 重新加载附件列表
      loadAttachments();
    } else {
      message.error(`文件 "${attachment.fileName}" 删除失败`);
    }
  } catch (error) {
    console.error('删除文件失败:', error);
    message.error(`文件 "${attachment.fileName}" 删除失败`);
  }
};

// 监听附件数据变化
watch(() => props.initialAttachments, () => {
  loadAttachments();
}, { immediate: true });

// 组件挂载时加载附件
onMounted(() => {
  loadAttachments();
});

// 暴露方法给父组件
defineExpose({
  loadAttachments,
  attachments: computed(() => attachments.value)
});
</script>

<style scoped>
.upload-section {
  margin-bottom: 16px;
}

.attachment-list {
  margin-top: 16px;
}

.empty-state {
  text-align: center;
  padding: 20px;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  font-size: 20px;
  margin-right: 12px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.file-meta {
  font-size: 12px;
  color: #666;
}

.file-meta span {
  margin-right: 12px;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}
</style>
