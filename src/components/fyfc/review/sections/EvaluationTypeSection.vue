<template>
    <!-- 评价类型区域 -->
    <div class="evaluation-type-section">
        <div class="section-title">
            <n-text strong>评价类型</n-text>
        </div>
        <n-space :wrap="true" :size="[8, 8]">
            <n-tag v-for="tag in evaluationTags" :key="tag.type"
                :type="tag.active ? 'primary' : (tag.available ? 'default' : 'info')" :bordered="false"
                :checkable="tag.available" :checked="tag.active"
                @update:checked="(checked) => toggleTag(tag.type, checked)" :size="isMobile ? 'medium' : 'large'"
                :class="['evaluation-tag', { 'tag-disabled': !tag.available }]">
                {{ tag.label }}
                <span v-if="!tag.available" style="margin-left: 4px; opacity: 0.6;">
                    (仅查看)
                </span>
            </n-tag>
        </n-space>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { NText, NSpace, NTag } from 'naive-ui';
import type { UserRole, EvaluationStatus } from '../../../../fconfig/fyfc/review';
import { getStatusLabel } from '../../../../fconfig/fyfc/review';

// 类型定义
export interface EvaluationScore {
    id?: number;
    evaluationId?: number;
    evaluator?: string;
    type: UserRole;
    performanceScore?: number | null;
    attitudeScore?: number | null;
    abilityScore?: number | null;
    growthScore?: number | null;
    score?: number;
    signature?: string;
}

export interface UserRoleConfig {
    type: UserRole;
    canEdit: boolean;
    canView: boolean;
}

// Props 定义
interface Props {
    scores?: EvaluationScore[];
    userRole?: UserRoleConfig;
    isMobile: boolean;
}

// Emits 定义
interface Emits {
    (e: 'toggleTag', type: EvaluationStatus, checked: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 状态到类型的映射
const statusToTypeMap: Record<EvaluationStatus, UserRole> = {
    'self': 'employee',
    'colleague': 'colleague',
    'manager': 'manager',
    'completed': 'admin'
};

// 评价标签
const evaluationTags = computed(() => {
    const scores = props.scores || [];
    const userRole = props.userRole?.type;

    // 所有可能的评价类型
    const allTags = [
        {
            type: 'self' as EvaluationStatus,
            label: getStatusLabel('self'),
            active: scores.some(score => score.type === 'employee'),
            available: true // 可用性由下面的过滤逻辑控制
        },
        {
            type: 'colleague' as EvaluationStatus,
            label: getStatusLabel('colleague'),
            active: scores.some(score => score.type === 'colleague'),
            available: true
        },
        {
            type: 'manager' as EvaluationStatus,
            label: getStatusLabel('manager'),
            active: scores.some(score => score.type === 'manager'),
            available: true
        }
    ];

    // 根据用户角色过滤显示的标签
    switch (userRole) {
        case 'employee':
            // 员工角色：只显示员工自评，且立即激活
            return allTags.filter(tag => tag.type === 'self').map(tag => ({
                ...tag,
                active: true // 员工角色立即激活自评标签
            }));

        case 'colleague':
            // 同事角色：只显示同事评价，且立即激活
            return allTags.filter(tag => tag.type === 'colleague').map(tag => ({
                ...tag,
                active: true // 同事角色立即激活同事评价标签
            }));

        case 'manager':
            // 主管角色：只显示主管审核，且立即激活
            return allTags.filter(tag => tag.type === 'manager').map(tag => ({
                ...tag,
                active: true // 主管角色立即激活主管审核标签
            }));

        case 'admin':
            // 管理员角色：显示所有类型，根据数据显示激活状态
            return allTags.map(tag => ({
                ...tag,
                available: false // 管理员仅查看，不可编辑
            }));

        default:
            // 未知角色：不显示任何标签
            return [];
    }
});

// 切换标签
const toggleTag = (type: EvaluationStatus, checked: boolean) => {
    emit('toggleTag', type, checked);
};
</script>

<style scoped>
/* 区域标题样式 */
.section-title {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

/* 评价类型区域 */
.evaluation-type-section {
    margin: 20px 0;
}

.evaluation-tag {
    margin: 4px;
    transition: all 0.3s ease;
}

.evaluation-tag:hover:not(.tag-disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-disabled {
    cursor: not-allowed !important;
    opacity: 0.7;
}

.tag-disabled:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .section-title {
        text-align: center;
    }

    .evaluation-tag {
        margin: 2px;
    }
}

/* 标签动画 */
:deep(.n-tag) {
    cursor: pointer;
    transition: all 0.3s ease;
}

:deep(.n-tag:hover) {
    transform: translateY(-1px);
}
</style>
