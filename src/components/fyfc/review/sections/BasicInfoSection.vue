<template>
    <!-- 基本信息区域 -->
    <div class="basic-info-section">
        <!-- 可编辑基础信息：员工自己或管理员 -->
        <n-form v-if="canEditBasicInfo" :model="formData"
            :label-placement="isMobile ? 'top' : 'left'" :label-width="isMobile ? 'auto' : '80px'"
            class="basic-form">
            <n-grid :cols="isMobile ? 1 : (screenWidth >= 1200 ? 3 : 2)" :x-gap="16" :y-gap="16">
                <n-form-item-gi label="部门">
                    <n-input v-model:value="formData.department" placeholder="请输入部门" />
                </n-form-item-gi>
                <n-form-item-gi label="姓名">
                    <n-input v-model:value="formData.name" placeholder="请输入姓名" />
                </n-form-item-gi>
                <n-form-item-gi label="评价日期">
                    <n-date-picker v-model:value="formData.reviewDate" type="date" placeholder="选择评价日期"
                        style="width: 100%" />
                </n-form-item-gi>
                <n-form-item-gi label="被邀同事">
                    <n-input v-model:value="formData.colleagueName" placeholder="请输入被邀同事姓名"
                        @input="handleColleagueNameInput" />
                </n-form-item-gi>
                <n-form-item-gi label="主管上级">
                    <n-input v-model:value="formData.managerName" placeholder="请输入主管上级姓名"
                        @input="handleManagerNameInput" />
                </n-form-item-gi>
            </n-grid>
        </n-form>

        <!-- 其他角色：紧凑只读显示 -->
        <div v-else class="basic-info-compact">
            <div class="compact-info-row">
                <div class="compact-info-item">
                    <span class="compact-label">部门:</span>
                    <n-text>{{ formData.department || '未填写' }}</n-text>
                </div>
                <span class="compact-separator">|</span>
                <div class="compact-info-item">
                    <span class="compact-label">姓名:</span>
                    <n-text strong>{{ formData.name || '未填写' }}</n-text>
                </div>
                <span class="compact-separator">|</span>
                <div class="compact-info-item">
                    <span class="compact-label">日期:</span>
                    <n-text>{{ formatDate(formData.reviewDate) || '未选择' }}</n-text>
                </div>
            </div>
            <div class="compact-info-row" style="margin-top: 8px;">
                <div class="compact-info-item">
                    <span class="compact-label">被邀同事:</span>
                    <n-text :style="{ color: formData.colleagueName ? '#333' : '#999' }">
                        {{ formData.colleagueName || '未设置' }}
                    </n-text>
                </div>
                <span class="compact-separator">|</span>
                <div class="compact-info-item">
                    <span class="compact-label">主管上级:</span>
                    <n-text :style="{ color: formData.managerName ? '#333' : '#999' }">
                        {{ formData.managerName || '未设置' }}
                    </n-text>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
    NForm,
    NFormItemGi,
    NGrid,
    NInput,
    NDatePicker,
    NText
} from 'naive-ui';
import type { UserRole } from '../../../../fconfig/fyfc/review';
import { useUserContext } from '../../../../utils/UserContext';

// 类型定义
export interface EvaluationData {
    id?: number;
    department?: string;
    name?: string;
    reviewDate?: number;
    colleagueName?: string;
    managerName?: string;
    additionalScore?: number;
    score?: number;
    comment?: string;
    createdAt?: number;
    createdBy?: string;
    updatedBy?: string;
    status?: string;
}

export interface UserRoleConfig {
    type: UserRole;
    canEdit: boolean;
    canView: boolean;
}

// Props 定义
interface Props {
    formData: EvaluationData;
    userRole?: UserRoleConfig;
    isMobile: boolean;
    screenWidth: number;
    canEdit: boolean;
}

// Emits 定义
interface Emits {
    (e: 'update:formData', value: EvaluationData): void;
    (e: 'colleagueNameInput', value: string): void;
    (e: 'managerNameInput', value: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 是否可以编辑基础信息（只有员工自己和管理员可以编辑）
const canEditBasicInfo = computed(() => {
    if (!props.canEdit) return false;

    // 管理员可以编辑
    if (props.userRole?.type === 'admin') return true;

    // 员工只能编辑自己的评价
    if (props.userRole?.type === 'employee') {
        const { getCurrentDisplayName } = useUserContext();
        const currentUser = getCurrentDisplayName();
        return props.formData.name === currentUser;
    }

    // 同事和主管不能编辑基础信息
    return false;
});

// 格式化日期
const formatDate = (timestamp?: number): string => {
    if (!timestamp) return '';
    return new Date(timestamp).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
};

// 事件处理
const handleColleagueNameInput = (value: string) => {
    emit('colleagueNameInput', value);
};

const handleManagerNameInput = (value: string) => {
    emit('managerNameInput', value);
};
</script>

<style scoped>
.basic-info-section {
    margin-bottom: 20px;
}

.basic-form {
    width: 100%;
}

/* 紧凑基础信息样式 */
.basic-info-compact {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.compact-info-row {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    font-size: 14px;
}

.compact-info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    white-space: nowrap;
}

.compact-label {
    font-weight: 500;
    color: #666;
    font-size: 13px;
}

.compact-separator {
    color: #ccc;
    margin: 0 4px;
    font-weight: 300;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .compact-info-row {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .compact-info-item {
        width: 100%;
        justify-content: flex-start;
    }

    .compact-separator {
        display: none;
    }
}

/* 深度样式 */
:deep(.n-form-item-label) {
    font-weight: 500;
}

:deep(.n-date-picker) {
    width: 100%;
}
</style>
