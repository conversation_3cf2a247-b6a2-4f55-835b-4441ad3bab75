<template>
    <div>
        <!-- 线上转发区域 -->
        <div class="additional-score-section">
            <div class="section-title">
                <n-text strong>线上转发</n-text>
                <n-text depth="3" style="font-size: 12px; margin-left: 8px">
                    (0-10分，由综合办统一填写)
                </n-text>
            </div>

            <!-- 管理员角色：可编辑 -->
            <div v-if="userRole?.type === 'admin'" class="additional-score-edit">
                <n-form-item label="线上转发评分">
                    <div class="additional-score-input">
                        <n-input-number
                            :value="additionalScore"
                            :min="0"
                            :max="10"
                            :step="1"
                            :precision="1"
                            placeholder="0-10"
                            :style="{ width: isMobile ? '100%' : '120px' }"
                            @update:value="handleAdditionalScoreChange"
                        >
                            <template #suffix>
                                分
                            </template>
                        </n-input-number>
                    </div>
                </n-form-item>
            </div>

            <!-- 其他角色：只读显示 -->
            <div v-else class="additional-score-readonly">
                <div class="readonly-score-item">
                    <div class="readonly-label">线上转发评分</div>
                    <div class="readonly-value">
                        <n-text strong style="font-size: 16px; color: #52c41a">
                            {{ (additionalScore || 0).toFixed(1) }} 分
                        </n-text>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最终得分区域 -->
        <div class="final-score-section">
            <div class="final-score-content">
                <div class="final-score-title">
                    <n-text strong style="font-size: 16px">最终得分</n-text>
                    <n-text depth="3" style="font-size: 12px; margin-left: 8px">
                        (员工自评×10% + 同事评分×20% + 主管评分×70% + 线上转发)
                        <br />当没有同事评分时 (员工自评×10% + 主管评分×90% + 线上转发)
                    </n-text>
                </div>
                <div class="final-score-value">
                    <n-text strong style="font-size: 20px; color: #f0a020">
                        {{ finalScore.toFixed(1) }} 分
                    </n-text>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
    NText,
    NFormItem,
    NInputNumber
} from 'naive-ui';
import type { UserRole } from '../../../../fconfig/fyfc/review';

// 类型定义
export interface EvaluationScore {
    id?: number;
    evaluationId?: number;
    evaluator?: string;
    type: UserRole;
    performanceScore?: number | null;
    attitudeScore?: number | null;
    abilityScore?: number | null;
    growthScore?: number | null;
    score?: number;
    signature?: string;
}

export interface UserRoleConfig {
    type: UserRole;
    canEdit: boolean;
    canView: boolean;
}

// Props 定义
interface Props {
    scores?: EvaluationScore[];
    additionalScore?: number;
    userRole?: UserRoleConfig;
    isMobile: boolean;
}

// Emits 定义
interface Emits {
    (e: 'updateAdditionalScore', value: number | null): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 类型映射：服务器类型 -> 前端类型
const mapServerTypeToFrontend = (serverType: string): UserRole => {
    switch (serverType) {
        case 'self':
            return 'employee';
        case 'colleague':
            return 'colleague';
        case 'manager':
            return 'manager';
        default:
            return serverType as UserRole;
    }
};

// 最终得分计算
const finalScore = computed(() => {
    const scores = props.scores || [];
    
    // 使用类型映射查找评分数据
    const employeeScore = scores.find(s => mapServerTypeToFrontend(s.type) === 'employee');
    const colleagueScore = scores.find(s => mapServerTypeToFrontend(s.type) === 'colleague');
    const managerScore = scores.find(s => mapServerTypeToFrontend(s.type) === 'manager');
    
    const employeeTotal = employeeScore ? 
        (employeeScore.performanceScore || 0) + 
        (employeeScore.attitudeScore || 0) + 
        (employeeScore.abilityScore || 0) + 
        (employeeScore.growthScore || 0) : 0;
    
    const colleagueTotal = colleagueScore ? 
        (colleagueScore.performanceScore || 0) + 
        (colleagueScore.attitudeScore || 0) + 
        (colleagueScore.abilityScore || 0) + 
        (colleagueScore.growthScore || 0) : 0;
    
    const managerTotal = managerScore ? 
        (managerScore.performanceScore || 0) + 
        (managerScore.attitudeScore || 0) + 
        (managerScore.abilityScore || 0) + 
        (managerScore.growthScore || 0) : 0;
    
    const additional = props.additionalScore || 0;
    
    // 如果没有同事评分，使用备用公式
    if (colleagueTotal === 0) {
        return employeeTotal * 0.1 + managerTotal * 0.9 + additional;
    }
    
    // 标准公式
    return employeeTotal * 0.1 + colleagueTotal * 0.2 + managerTotal * 0.7 + additional;
});

// 事件处理
const handleAdditionalScoreChange = (value: number | null) => {
    emit('updateAdditionalScore', value);
};
</script>

<style scoped>
/* 区域标题样式 */
.section-title {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

/* 线上转发区域 */
.additional-score-section {
    margin: 20px 0;
}

.additional-score-edit {
    margin-top: 16px;
}

.additional-score-input {
    display: flex;
    align-items: center;
    gap: 8px;
}

.additional-score-readonly {
    margin-top: 16px;
}

.readonly-score-item {
    padding: 12px 16px;
    background-color: #fafafa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.readonly-label {
    font-weight: 500;
    color: #666;
    font-size: 14px;
}

.readonly-value {
    display: flex;
    align-items: center;
}

/* 最终得分区域 */
.final-score-section {
    margin: 20px 0;
}

.final-score-content {
    padding: 16px 20px;
    background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
    border: 2px solid #f0a020;
    border-radius: 8px;
    text-align: center;
}

.final-score-title {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.final-score-value {
    font-size: 24px;
    font-weight: bold;
}

/* 移动端优化 */
@media (max-width: 768px) {
    .additional-score-input {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .readonly-score-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}
</style>
