# FYFC Review Components

## 📋 **组件概述**

FYFC Review 组件库提供了完整的绩效评价功能，支持多角色评价流程。

### **核心组件**
- **EvaluationEditorNew.vue** - 主评价编辑器（推荐使用）
- **EvaluationEditor.vue** - 旧版评价编辑器（保留兼容）

### **功能区块组件 (sections/)**
- **BasicInfoSection.vue** - 基础信息编辑
- **EvaluationTypeSection.vue** - 评价类型显示
- **ScoringSection.vue** - 评分功能
- **ActionSection.vue** - 操作按钮
- **FinalScoreSection.vue** - 最终得分显示
- **AttachmentSectionFixed.vue** - 附件管理（上传、下载、删除、预览）⭐

## 🎯 **基本用法**

```vue
<template>
  <EvaluationEditorNew
    :evaluation-data="evaluationData"
    :user-role="userRole"
    :can-edit="canEdit"
    :page-mode="pageMode"
    @save="handleSave"
    @reset="handleReset"
    @back="handleBack"
    @score-submitted="handleScoreSubmitted"
  />
</template>

<script setup lang="ts">
import EvaluationEditorNew from '@/components/fyfc/review/EvaluationEditorNew.vue';
import type { EvaluationData, UserRole } from '@/types/fyfc/review';

// 基本配置
const evaluationData = ref<EvaluationData>({
  id: 1,
  status: 'self', // 评价状态
  createdBy: 'user1', // 创建人
  /* 其他评价数据 */
});
const userRole = ref<UserRole>({ type: 'employee', canEdit: true });
const pageMode = ref<'create' | 'edit' | 'view'>('edit');

// 事件处理
const handleSave = (data: EvaluationData) => { /* 保存逻辑 */ };
const handleReset = () => { /* 重置逻辑 */ };
const handleBack = (userRole?: string) => { /* 返回逻辑 */ };
const handleScoreSubmitted = (id: number, type: UserRole) => { /* 评分提交 */ };
</script>
```

### **AttachmentSectionFixed 单独使用**

```vue
<template>
  <AttachmentSectionFixed
    :evaluation-id="evaluationId"
    :upload-by="currentUser"
    :evaluation-status="evaluationStatus"
    :evaluation-created-by="evaluationCreatedBy"
    :current-user="currentUser"
    :initial-attachments="attachments"
    @attachments-updated="handleAttachmentsUpdated"
  />
</template>

<script setup lang="ts">
import AttachmentSectionFixed from '@/components/fyfc/review/sections/AttachmentSectionFixed.vue';

const evaluationId = ref(1);
const currentUser = ref('current_user');
const evaluationStatus = ref('self'); // 'self' | 'colleague' | 'manager' | 'completed'
const evaluationCreatedBy = ref('creator_user');

const handleAttachmentsUpdated = (attachments) => {
  console.log('附件更新:', attachments);
};
</script>
```

## 📊 **核心数据结构**

### **EvaluationData**
```typescript
interface EvaluationData {
  id?: number;
  department: string;
  name: string;
  reviewDate: number;
  colleagueName?: string;
  managerName?: string;
  scores: ScoreData[];
  comment?: string;
  attachments?: string; // 附件JSON字符串
  additionalScore?: number;
  status?: string;
}
```

### **UserRole**
```typescript
interface UserRole {
  type: 'employee' | 'colleague' | 'manager' | 'admin';
  canEdit: boolean;
  canView: boolean;
}
```

## 🔧 **权限控制**

组件会根据 `userRole.type` 自动控制功能显示：
- **employee**: 可以自评和编辑基础信息
- **colleague**: 可以进行同事评价
- **manager**: 可以进行主管评分
- **admin**: 可以查看所有数据和进行管理操作

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `EvaluationData` | 默认空对象 | 评价数据 |
| `userRole` | `UserRoleConfig` | `{ type: 'employee', canEdit: true, canView: true }` | 用户角色配置 |
| `title` | `string` | `'方远房地产集团员工月度绩效考核评分表'` | 卡片标题 |
| `showActions` | `boolean` | `true` | 是否显示操作按钮 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `EvaluationData` | 数据更新时触发 |
| `save` | `EvaluationData` | 点击保存按钮时触发 |
| `reset` | - | 点击重置按钮时触发 |
| `preview` | `EvaluationData` | 点击预览按钮时触发 |

## 类型定义

### EvaluationData

```typescript
interface EvaluationData {
  id?: number;                          // 评价ID
  department?: string;                  // 部门
  name?: string;                       // 姓名
  reviewDate?: number | null;          // 评价日期时间戳
  evaluationTypes: EvaluationStatus[]; // 评价类型数组
  scores: {
    performanceScore: number | null;   // 绩效得分
    attitudeScore: number | null;      // 态度得分
    abilityScore: number | null;       // 能力得分
    growthScore: number | null;        // 个人成长得分
  };
  score?: number;                      // 小计
  comment?: string;                    // 说明备注
  additionalScore?: number;            // 线上转发
  status?: string;                     // 状态
  createdAt?: number;                  // 创建时间
  createdBy?: string;                  // 创建人
  updatedBy?: string;                  // 更新人
}
```

### UserRoleConfig

```typescript
interface UserRoleConfig {
  type: UserRole;      // 用户角色类型
  canEdit: boolean;    // 是否可编辑
  canView: boolean;    // 是否可查看
}

// UserRole 来自配置文件
type UserRole = 'unknown' | 'employee' | 'colleague' | 'manager' | 'admin';

// EvaluationStatus 来自配置文件
type EvaluationStatus = 'self' | 'colleague' | 'manager' | 'completed';
```

## 评分等级 (基于得分率)

| 得分率范围 | 等级 | 标签颜色 | 示例分数 |
|------------|------|----------|----------|
| 90-100% | 优秀 | success (绿色) | 81-90分 |
| 80-89% | 良好 | info (蓝色) | 72-80分 |
| 70-79% | 合格 | warning (橙色) | 63-71分 |
| 60-69% | 待改进 | warning (橙色) | 54-62分 |
| 0-59% | 不合格 | error (红色) | 0-53分 |

**说明**: 等级评定基于得分率而非绝对分数，确保评价标准的一致性。

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以使用深度选择器：

```vue
<style>
:deep(.evaluation-card) {
  /* 自定义卡片样式 */
}

:deep(.subtotal-container) {
  /* 自定义小计容器样式 */
}
</style>
```

## 响应式设计

组件内置完善的响应式设计，针对不同屏幕尺寸优化：

### 桌面端 (>768px)
- 基本信息采用三列网格布局
- 评分项目采用网格卡片布局
- 标签和按钮使用较大尺寸

### 移动端 (≤768px)
- 基本信息改为单列布局，标签置顶
- 评分项目改为单列卡片布局
- 评分输入框占满宽度
- 小计信息垂直排列
- 按钮适配移动端尺寸

### 小屏幕 (≤480px)
- 进一步优化间距和内边距
- 按钮组自适应排列
- 文本输入框增加行数

### 移动端特性
- 自动检测屏幕尺寸
- 触摸友好的交互元素
- 优化的滚动体验
- 防止意外缩放

## 验证规则

保存时会进行以下验证：

1. ✅ 基本信息完整性（部门、姓名、日期）
2. ✅ 至少选择一种评价类型
3. ✅ 至少填写一项评分

## 注意事项

1. 日期使用时间戳格式存储
2. 评分范围为 0-100 分，支持一位小数
3. 组件依赖 Naive UI，确保项目中已安装
4. 使用 `v-model` 进行双向数据绑定
5. 所有事件都会传递完整的数据副本

## 🔧 **附件管理功能**

### **AttachmentSection组件**
- 使用 `n-upload` 组件和 `custom-request` 定制上传
- 支持拖拽上传和批量上传
- 文件类型和大小验证（单文件最大50MB）
- 附件列表显示（文件名、大小、上传者、时间）
- 下载、预览、删除功能
- **智能权限控制**：
  - `completed` 状态：所有用户只能查看和下载
  - 非 `completed` 状态：只有评价创建人可以上传和删除
  - 其他用户始终可以查看和下载附件

### **OSS集成**
- 自动调用OSS API进行文件管理
- 附件信息存储在evaluation的attachments字段
- 支持多种文件类型（图片、文档、PDF等）
- 文件预览功能（图片和文档类型）

## 📝 **开发注意事项**

- 确保传入正确的角色信息和权限标识
- 评分数值会自动进行范围验证（绩效0-60分，其他0-10分）
- 组件内部处理了权限控制和数据验证
- 支持分享链接和加密参数传递
- 附件功能需要有效的evaluationId才能使用
- 附件数量会在dashboard列表中显示

## 示例项目

参考 `src/views/fyfc/review/staff/edit/index.vue` 查看完整的使用示例。

## 📚 **相关文档**

详细的API文档和使用示例请参考：
- **`/src/FYFC_ATTACHMENT_SYSTEM_DOCUMENTATION.md`** - 附件系统完整文档 ⭐
- **`/src/FYFC_REVIEW_SYSTEM_DOCUMENTATION.md`** - 系统整体文档
- **`/src/FYFC_OSS_API_DOCUMENTATION.md`** - OSS接口文档
- **`/src/fyfc-oss-test.html`** - OSS功能测试页面

## 🎯 **最新更新**

### **v1.0 附件系统**
- ✅ 完整的附件管理功能
- ✅ OSS云存储集成
- ✅ 中文字符编码处理
- ✅ 智能权限控制
- ✅ 错误处理和用户反馈
- ✅ 响应式设计优化
