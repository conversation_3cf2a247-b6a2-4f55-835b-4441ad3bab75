<template>
    <n-card :title="cardTitle" size="small" :bordered="true" class="evaluation-card">
        <!-- 基本信息区域 -->
        <BasicInfoSection
            :formData="formData"
            :userRole="userRole"
            :isMobile="isMobile"
            :screenWidth="screenWidth"
            :canEdit="canEdit"
            @colleagueNameInput="handleColleagueNameInput"
            @managerNameInput="handleManagerNameInput"
        />

        <n-divider />

        <!-- 评分区域 - 仅在有评价ID时显示 -->
        <div v-if="hasEvaluationId" class="evaluation-scoring-area">
            <!-- 评价类型区域 -->
            <EvaluationTypeSection
                :scores="formData.scores"
                :userRole="userRole"
                :isMobile="isMobile"
                @toggleTag="handleToggleTag"
            />

            <n-divider />

            <!-- 评分项目区域 -->
            <ScoringSection
                :scores="formData.scores"
                :userRole="userRole"
                :currentScoreType="currentScoreType"
                :isMobile="isMobile"
                :canEdit="canEdit"
                @updateScore="handleUpdateScore"
                @scoreTypeChange="handleScoreTypeChange"
            />
        </div>

        <!-- 未保存基础信息时的提示 -->
        <div v-else class="save-basic-info-prompt">
            <n-divider />
            <div class="prompt-content">
                <n-text strong style="font-size: 16px; color: #f0a020">
                    📝 请先保存基础信息
                </n-text>
                <n-text depth="3" style="font-size: 14px; margin-top: 8px; display: block;">
                    填写完整的基础信息（部门、姓名、评价日期、同事、主管）并保存后，即可进行评分操作。
                </n-text>
            </div>
        </div>

        <!-- 最终得分区域 -->
        <n-divider />
        <FinalScoreSection
            :scores="formData.scores"
            :additionalScore="formData.additionalScore"
            :userRole="userRole"
            :isMobile="isMobile"
            @updateAdditionalScore="handleUpdateAdditionalScore"
        />

        <!-- 备注区域 -->
        <n-divider />
        <div class="remarks-section">
            <div class="section-title">
                <n-text strong>备注说明</n-text>
                <n-text v-if="userRole?.type !== 'admin'" depth="3" style="font-size: 12px; margin-left: 8px">
                    (仅管理员可编辑)
                </n-text>
            </div>
            <n-input v-model:value="formData.comment" type="textarea" placeholder="请输入备注信息..." :rows="isMobile ? 4 : 3"
                :disabled="userRole?.type !== 'admin'" class="remarks-input" />
        </div>

        <!-- 修复版本附件组件测试 -->
        <div v-if="hasEvaluationId && showAttachmentFixed">
            <n-divider />
            <AttachmentSectionFixed
                :evaluation-id="formData.id"
                :upload-by="getCurrentUsername() || 'current_user'"
                :evaluation-status="formData.status"
                :evaluation-created-by="formData.createdBy"
                :current-user="getCurrentUsername() || 'current_user'"
                :readonly="!canEdit"
                :initial-attachments="formData.attachments"
                @attachments-updated="handleAttachmentsUpdatedFixed"
            />
        </div>

        <!-- 操作按钮区域 -->
        <ActionSection
            :formData="formData"
            :userRole="userRole"
            :isMobile="isMobile"
            :canEdit="canEdit"
            :showActions="showActions"
            @back="handleBack"
            @reset="handleReset"
            @save="handleSave"
            @submitScore="handleSubmitScore"
            @share="handleShare"
        />
    </n-card>
</template>

<script setup lang="ts">
import { computed, reactive, watch, onMounted, onUnmounted, ref } from 'vue';
import { NCard, NDivider, NText, NInput, useMessage } from 'naive-ui';
import type { UserRole, EvaluationStatus } from '../../../fconfig/fyfc/review';
import { fyfcReviewApi, type ScoreFormDto } from '../../../utils/FyfcReviewApi';
import { generateEncryptedUrl } from '../../../utils/crypto';
import { useUserContext } from '../../../utils/UserContext';

// 导入子组件
import BasicInfoSection from './sections/BasicInfoSection.vue';
import EvaluationTypeSection from './sections/EvaluationTypeSection.vue';
import ScoringSection from './sections/ScoringSection.vue';
import FinalScoreSection from './sections/FinalScoreSection.vue';
import ActionSection from './sections/ActionSection.vue';
import AttachmentSectionFixed from './sections/AttachmentSectionFixed.vue'; // 修复版本附件组件

// 类型定义
export interface EvaluationData {
    id?: number;
    department?: string;
    name?: string;
    reviewDate?: number;
    colleagueName?: string;
    managerName?: string;
    additionalScore?: number;
    score?: number;
    comment?: string;
    attachments?: string; // 新增：附件JSON字符串
    createdAt?: number;
    createdBy?: string;
    updatedBy?: string;
    status?: string;
    scores?: EvaluationScore[];
}

interface EvaluationScore {
    id?: number;
    evaluationId?: number;
    evaluator?: string;
    type: UserRole;
    performanceScore?: number | null;
    attitudeScore?: number | null;
    abilityScore?: number | null;
    growthScore?: number | null;
    score?: number;
    signature?: string;
}

export interface UserRoleConfig {
    type: UserRole;
    canEdit: boolean;
    canView: boolean;
}

// Props 定义
interface Props {
    modelValue?: EvaluationData;
    userRole?: UserRoleConfig;
    title?: string;
    showActions?: boolean;
}

// Emits 定义
interface Emits {
    (e: 'update:modelValue', value: EvaluationData): void;
    (e: 'save', value: EvaluationData): void;
    (e: 'reset'): void;
    (e: 'back', userRole?: UserRole): void;
    (e: 'scoreSubmitted', evaluationId: number, scoreType: UserRole): void;
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
        department: '',
        name: '',
        reviewDate: undefined,
        colleagueName: '',
        managerName: '',
        scores: [],
        comment: '',
        attachments: undefined
    }),
    userRole: () => ({
        type: 'employee',
        canEdit: true,
        canView: true
    }),
    title: '方远房地产集团员工月度绩效考核评分表',
    showActions: true
});

const emit = defineEmits<Emits>();
const message = useMessage();
const { getCurrentDisplayName, getCurrentUsername } = useUserContext();

// 移动端检测和屏幕宽度
const isMobile = ref(false);
const screenWidth = ref(window.innerWidth);

const checkMobile = () => {
    isMobile.value = window.innerWidth <= 768;
    screenWidth.value = window.innerWidth;
};

onMounted(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
    window.removeEventListener('resize', checkMobile);
});

// 响应式数据
const formData = reactive<EvaluationData>({ ...props.modelValue });

// 当前选中的评分类型（用于管理员切换查看）
const currentScoreType = ref<UserRole>(props.userRole?.type || 'employee');

// 监听 userRole 变化，同步更新 currentScoreType
watch(() => props.userRole?.type, (newType) => {
    if (newType) {
        currentScoreType.value = newType;
    }
}, { immediate: true });

// 计算属性
const cardTitle = computed(() => props.title);
const canEdit = computed(() => props.userRole?.canEdit ?? true);

// 是否有评价ID（用于判断是否显示评分区域）
const hasEvaluationId = computed(() => {
    return formData.id !== undefined && formData.id !== null;
});

// 控制是否显示修复版本附件组件（用于调试）
const showAttachmentFixed = ref(true); // 设置为true来测试修复版本附件组件

// 最终得分计算
const finalScore = computed(() => {
    const scores = formData.scores || [];

    // 使用类型映射查找评分数据
    const mapServerTypeToFrontend = (serverType: string): UserRole => {
        switch (serverType) {
            case 'self': return 'employee';
            case 'colleague': return 'colleague';
            case 'manager': return 'manager';
            default: return serverType as UserRole;
        }
    };

    const employeeScore = scores.find(s => mapServerTypeToFrontend(s.type) === 'employee');
    const colleagueScore = scores.find(s => mapServerTypeToFrontend(s.type) === 'colleague');
    const managerScore = scores.find(s => mapServerTypeToFrontend(s.type) === 'manager');

    const employeeTotal = employeeScore ?
        (employeeScore.performanceScore || 0) +
        (employeeScore.attitudeScore || 0) +
        (employeeScore.abilityScore || 0) +
        (employeeScore.growthScore || 0) : 0;

    const colleagueTotal = colleagueScore ?
        (colleagueScore.performanceScore || 0) +
        (colleagueScore.attitudeScore || 0) +
        (colleagueScore.abilityScore || 0) +
        (colleagueScore.growthScore || 0) : 0;

    const managerTotal = managerScore ?
        (managerScore.performanceScore || 0) +
        (managerScore.attitudeScore || 0) +
        (managerScore.abilityScore || 0) +
        (managerScore.growthScore || 0) : 0;

    const additional = formData.additionalScore || 0;

    // 如果没有同事评分，使用备用公式
    if (colleagueTotal === 0) {
        return employeeTotal * 0.1 + managerTotal * 0.9 + additional;
    }

    // 标准公式
    return employeeTotal * 0.1 + colleagueTotal * 0.2 + managerTotal * 0.7 + additional;
});

// 监听finalScore变化，自动更新formData.score
watch(finalScore, (newFinalScore) => {
    formData.score = newFinalScore;
});

// 事件处理方法
const handleColleagueNameInput = (value: string) => {
    // 处理被邀同事姓名输入，防止空格（对照旧版逻辑）
    formData.colleagueName = value.replace(/\s/g, '');
};

const handleManagerNameInput = (value: string) => {
    // 处理主管上级姓名输入，防止空格（对照旧版逻辑）
    formData.managerName = value.replace(/\s/g, '');
};

const handleToggleTag = (_type: EvaluationStatus, _checked: boolean) => {
    // 处理评价类型标签切换
    // 标签切换逻辑
};

const handleUpdateScore = (key: keyof EvaluationScore, value: number | null) => {
    // 处理评分更新
    const scoreType = props.userRole?.type === 'admin' ? currentScoreType.value : props.userRole?.type;
    if (!scoreType) return;

    // 确保scores数组存在
    if (!formData.scores) {
        formData.scores = [];
    }

    // 查找或创建对应类型的评分记录
    let scoreRecord = formData.scores.find(score =>
        score.type === scoreType ||
        (scoreType === 'employee' && (score.type as string) === 'self')
    );

    if (!scoreRecord) {
        // 创建新的评分记录
        scoreRecord = {
            type: scoreType,
            evaluationId: formData.id,
            performanceScore: null,
            attitudeScore: null,
            abilityScore: null,
            growthScore: null
        };
        formData.scores.push(scoreRecord);
    }

    // 更新对应的评分字段
    (scoreRecord as any)[key] = value;
};

const handleScoreTypeChange = (type: UserRole) => {
    currentScoreType.value = type;
};

const handleUpdateAdditionalScore = (value: number | null) => {
    formData.additionalScore = value ?? undefined;
};

const handleBack = () => {
    emit('back', props.userRole?.type);
};

const handleReset = () => {
    // 对照旧版的重置逻辑
    Object.assign(formData, {
        department: '',
        name: '',
        reviewDate: null,
        colleagueName: '',
        managerName: '',
        scores: [],
        comment: '',
        attachments: undefined,
        additionalScore: null
    });
    emit('reset');
    message.info('已重置表单');
};

const handleSave = () => {
    // 验证必填项
    if (!formData.department || !formData.name || !formData.reviewDate) {
        message.error('请填写完整的基本信息');
        return;
    }

    // 如果没有评价ID，说明是新增模式，只需要保存基础信息
    if (!formData.id) {
        const dataToSave = {
            ...formData,
            updatedBy: getCurrentUsername() || 'current_user',
            createdAt: formData.createdAt || Date.now()
        };

        emit('save', dataToSave);
        if (props.userRole?.type === 'admin') {
            message.success('评价信息保存成功');
            return;
        }
        message.success('基础信息保存成功，现在可以进行评分了');
        return;
    }

    // 如果有评价ID，说明是编辑模式，需要验证评分数据
    const scores = formData.scores || [];
    if (scores.length === 0) {
        // 编辑模式下，如果没有评分数据，只保存基础信息
        const dataToSave = {
            ...formData,
            score: finalScore.value,
            updatedBy: getCurrentUsername() || 'current_user',
            createdAt: formData.createdAt || Date.now()
        };

        emit('save', dataToSave);
        message.success('基础信息保存成功');
        return;
    }

    // 如果有评分数据，验证评分的有效性
    const hasValidScore = scores.some(score =>
        (score.performanceScore || 0) +
        (score.attitudeScore || 0) +
        (score.abilityScore || 0) +
        (score.growthScore || 0) > 0
    );

    const dataToSave = {
        ...formData,
        score: finalScore.value,
        updatedBy: getCurrentUsername() || 'current_user',
        createdAt: formData.createdAt || Date.now()
    };

    emit('save', dataToSave);

    if (hasValidScore) {
        message.success('评价数据保存成功');
    } else {
        message.success('基础信息保存成功');
    }
};

const handleSubmitScore = async () => {
    // 提交评分逻辑


    try {
        if (!formData.id || !props.userRole?.type) {
            message.error('缺少必要的评价信息');
            return;
        }

        // 获取当前用户的评分数据
        const currentScore = formData.scores?.find(score =>
            score.type === props.userRole?.type ||
            (props.userRole?.type === 'employee' && (score.type as string) === 'self')
        );

        if (!currentScore) {
            message.error('未找到评分数据');
            return;
        }

        // 验证评分数据完整性
        const total = (currentScore.performanceScore || 0) +
                     (currentScore.attitudeScore || 0) +
                     (currentScore.abilityScore || 0) +
                     (currentScore.growthScore || 0);

        if (total <= 0) {
            message.error('请先完成评分');
            return;
        }

        // 构建提交数据
        const scoreFormDto: ScoreFormDto = {
            evaluationId: formData.id,
            type: props.userRole.type,
            performanceScore: currentScore.performanceScore || 0,
            attitudeScore: currentScore.attitudeScore || 0,
            abilityScore: currentScore.abilityScore || 0,
            growthScore: currentScore.growthScore || 0,
            evaluationScore: finalScore.value // 使用最终得分而不是简单的total
        };

        // 根据用户角色调用不同的API
        let response;
        const currentUser = getCurrentDisplayName();

        switch (props.userRole.type) {
            case 'employee':
                response = await fyfcReviewApi.staff.submitSelfScore(scoreFormDto, currentUser);
                break;
            case 'colleague':
                response = await fyfcReviewApi.staff.submitColleagueScore(scoreFormDto, currentUser);
                break;
            case 'manager':
                response = await fyfcReviewApi.manager.submitManagerScore(scoreFormDto, currentUser);
                break;
            default:
                message.error('无效的用户角色');
                return;
        }

        if (response.success) {
            message.success('评分提交成功');
            emit('scoreSubmitted', formData.id, props.userRole.type);
        } else {
            message.error(response.message || '评分提交失败');
        }

    } catch (error) {
        console.error('提交评分失败:', error);
        message.error('评分提交失败，请重试');
    }
};

// 修复版本附件处理方法
const handleAttachmentsUpdatedFixed = (attachments: any[]) => {
    // 处理附件更新
    // 将附件信息更新到formData中
    formData.attachments = JSON.stringify(attachments);

    // 触发数据更新事件
    emit('update:modelValue', formData);
};

const handleShare = async () => {
    // 分享链接逻辑
    console.log('分享链接');

    try {
        if (!formData.id) {
            message.error('评价ID不存在，无法生成分享链接');
            return;
        }

        // 生成加密的分享链接
        const shareUrl = generateEncryptedUrl('/vfyg/fyfc/review/staff/edit', {
            id: formData.id,
            mode: 'edit'
        });

        // 构建完整的URL
        const fullUrl = `${window.location.origin}${shareUrl}`;

        // 复制到剪贴板
        try {
            await navigator.clipboard.writeText(fullUrl);
            message.success('分享链接已复制到剪贴板');
        } catch (clipboardError) {
            // 如果剪贴板API不可用，使用传统方法
            const textArea = document.createElement('textarea');
            textArea.value = fullUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            message.success('分享链接已复制到剪贴板');
        }

        console.log('生成的分享链接:', fullUrl);

    } catch (error) {
        console.error('生成分享链接失败:', error);
        message.error('生成分享链接失败，请重试');
    }
};

// 监听用户角色变化，确保评分数据结构（对照旧版逻辑）
watch(() => props.userRole?.type, (newRole, oldRole) => {
    if (newRole && newRole !== oldRole) {
        // 确保 scores 数组存在
        if (!formData.scores) {
            formData.scores = [];
        }

        // 根据角色自动创建对应的评分记录（如果不存在）
        const scores = formData.scores;
        const hasCurrentRoleScore = scores.some(score => score.type === newRole);

        if (!hasCurrentRoleScore && newRole !== 'admin') {
            const newScore: EvaluationScore = {
                type: newRole,
                performanceScore: undefined,
                attitudeScore: undefined,
                abilityScore: undefined,
                growthScore: undefined
            };
            scores.push(newScore);
        }
    }
}, { immediate: true });

// 监听 props 变化
watch(() => props.modelValue, (newValue) => {
    Object.assign(formData, newValue);
}, { deep: true });

// 监听表单数据变化，向上传递
watch(formData, (newValue) => {
    emit('update:modelValue', { ...newValue });
}, { deep: true });
</script>

<style scoped>
.evaluation-card {
    max-width: 100%;
    margin: 0 auto;
}

/* 评分区域 */
.evaluation-scoring-area {
    margin: 20px 0;
}

/* 区域标题样式 */
.section-title {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
    display: flex;
    align-items: center;
}

/* 备注区域 */
.remarks-section {
    margin: 20px 0;
}

.remarks-input {
    margin-top: 8px;
}

/* 保存基础信息提示样式 */
.save-basic-info-prompt {
    margin: 20px 0;
}

.prompt-content {
    padding: 20px;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border: 1px solid #f0a020;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(240, 160, 32, 0.1);
}

@media (max-width: 768px) {
    .evaluation-card {
        margin: 0;
        border-radius: 0;
    }

    .prompt-content {
        padding: 16px;
    }
}

/* 深度样式 */
:deep(.n-card-header) {
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

/* 确保子组件样式正确传递 */
:deep(.evaluation-tag) {
    cursor: pointer;
    transition: all 0.3s ease;
}

:deep(.evaluation-tag:hover) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

:deep(.tag-disabled) {
    cursor: not-allowed;
    opacity: 0.6;
}

:deep(.score-input) {
    transition: all 0.3s ease;
}

:deep(.score-input:focus-within) {
    box-shadow: 0 0 0 2px rgba(24, 160, 88, 0.2);
}
</style>
