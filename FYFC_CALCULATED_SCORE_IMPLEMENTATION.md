# FYFC 评价系统 - calculatedScore 字段实现总结

## 📋 实现概述

在 FYFC 评价系统的管理员 dashboard 中，在"合计"列右侧添加了"最终得分"列，用于显示 `calculatedScore` 字段。该字段是数据库表中的新增字段，不需要前端计算。

## ✅ 已完成的工作

### 1. 类型定义更新

#### `src/fconfig/fyfc/review/index.ts`
```typescript
type Evaluation = {
    // ... 其他字段
    calculatedScore?: number; //计算得分
    // ... 其他字段
}
```

#### `src/utils/FyfcReviewApi.ts`
更新了以下接口：
- `EvaluationDto` - 添加 `calculatedScore?: number;`
- `EvaluationFormDto` - 添加 `calculatedScore?: number;`
- `EvaluationUpdateDto` - 添加 `calculatedScore?: number;`

### 2. 表格配置更新

#### `src/views/fyfc/review/admin/dashboard/tableConfig.ts`
- 在 `EvaluationTableRow` 接口中添加 `calculatedScore?: number;`
- 在表格列配置中添加"最终得分"列：
```typescript
{
    title: '最终得分',
    key: 'calculatedScore',
    minWidth: 80,
    width: 90,
    render(row) {
        return row.calculatedScore?.toFixed(1) || '0.0';
    }
}
```
- 更新表格总宽度计算：从 1364px 增加到 1454px

### 3. 导出功能更新

#### `src/utils/export/excelExport.ts`
在 Excel 导出数据中添加"最终得分"列：
```typescript
const exportData = data.map(row => ({
    // ... 其他列
    '合计': row.totalScore?.toFixed(1) || '0.0',
    '最终得分': row.calculatedScore?.toFixed(1) || '0.0'
}));
```

### 4. 数据库结构更新

#### `database/fyfc_review_tables.sql`
在 `fyfc_evaluations` 表中添加字段：
```sql
`calculated_score` decimal(6,2) DEFAULT NULL COMMENT '最终计算得分',
```

更新视图 `v_evaluation_summary`：
```sql
SELECT
    -- ... 其他字段
    e.calculated_score,
    -- ... 其他字段
FROM fyfc_evaluations e;
```

#### `database/migrations/add_calculated_score_field.sql`
创建了数据库迁移脚本，用于在现有数据库中安全地添加 `calculated_score` 字段。

### 5. 前端数据处理

#### `src/views/fyfc/review/admin/dashboard/index.vue`
- 更新滚动宽度设置：从 1364px 增加到 1454px
- 在数据处理中保留 `calculatedScore` 字段（直接从数据库获取，不进行前端计算）
- 导出功能中同样保留 `calculatedScore` 字段

## 🔍 字段说明

### totalScore vs calculatedScore

- **totalScore（合计）**: 前端根据各项评分计算得出的总分
  - 计算公式：员工自评×10% + 同事评分×20% + 主管评分×70% + 线上转发
  - 当同事评分为0时：员工自评×10% + 主管评分×90% + 线上转发

- **calculatedScore（最终得分）**: 数据库中存储的最终计算得分
  - 由后端系统计算并存储
  - 前端直接显示，不进行计算

## 📊 表格列顺序

管理员 dashboard 表格列顺序（从左到右）：
1. 状态
2. 部门/项目
3. 姓名
4. 日期
5. 备注
6. 个人
7. 同事
8. 上级
9. 上级姓名
10. 转发
11. **合计** (totalScore)
12. **最终得分** (calculatedScore) ← 新增
13. 操作

## 🚀 部署说明

### 数据库迁移
执行以下脚本添加新字段：
```bash
mysql -u username -p database_name < database/migrations/add_calculated_score_field.sql
```

### 前端部署
所有前端代码已更新，支持 `calculatedScore` 字段的显示和导出。

## ⚠️ 注意事项

1. **数据源**: `calculatedScore` 字段来自数据库，前端不进行计算
2. **向后兼容**: 新字段允许为 NULL，不影响现有数据
3. **导出功能**: Excel 导出包含"最终得分"列
4. **表格宽度**: 表格总宽度增加了 90px，可能需要调整响应式布局

## 🔧 后续工作

需要后端开发人员：
1. 执行数据库迁移脚本
2. 更新 API 接口，确保返回 `calculatedScore` 字段
3. 实现 `calculatedScore` 的计算逻辑并存储到数据库

## 📝 测试建议

1. 验证表格显示是否正确
2. 测试导出功能是否包含新列
3. 检查响应式布局是否正常
4. 确认数据库字段添加成功
