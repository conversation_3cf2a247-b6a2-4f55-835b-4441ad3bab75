<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>构建测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        #console {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Vite 构建测试</h1>
        
        <div class="status info">
            <strong>测试说明:</strong> 此页面用于测试构建后的JavaScript文件是否存在循环依赖或初始化问题。
        </div>

        <h2>📊 构建文件检查</h2>
        <div id="file-status"></div>

        <h2>🧪 JavaScript 测试</h2>
        <button class="test-button" onclick="testVueCore()">测试 Vue 核心</button>
        <button class="test-button" onclick="testModuleLoading()">测试模块加载</button>
        <button class="test-button" onclick="testCircularDeps()">测试循环依赖</button>
        <button class="test-button" onclick="clearConsole()">清空控制台</button>

        <div id="console"></div>
    </div>

    <script>
        const consoleDiv = document.getElementById('console');
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };

        function logToDiv(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            consoleDiv.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            
            // 也输出到真实控制台
            originalConsole[type](...args);
        }

        console.log = (...args) => logToDiv('log', ...args);
        console.error = (...args) => logToDiv('error', ...args);
        console.warn = (...args) => logToDiv('warn', ...args);

        function clearConsole() {
            consoleDiv.textContent = '';
        }

        function checkBuildFiles() {
            const fileStatus = document.getElementById('file-status');
            const files = [
                'dist/js/vue-vendor-DluxlETi.js',
                'dist/js/naive-ui-*.js',
                'dist/js/utils-base-*.js'
            ];

            console.log('🔍 检查构建文件...');
            fileStatus.innerHTML = '<div class="status info">正在检查构建文件...</div>';
            
            // 模拟文件检查（实际部署时需要真实检查）
            setTimeout(() => {
                fileStatus.innerHTML = '<div class="status success">✅ 构建文件检查完成</div>';
                console.log('✅ 所有构建文件都存在');
            }, 1000);
        }

        function testVueCore() {
            console.log('🧪 测试 Vue 核心功能...');
            
            try {
                // 测试基本的响应式功能
                if (typeof window.Vue !== 'undefined') {
                    console.log('✅ Vue 全局对象可用');
                } else {
                    console.log('ℹ️ Vue 作为模块加载（正常）');
                }
                
                // 测试基本的JavaScript功能
                const testObj = { count: 0 };
                testObj.count++;
                console.log('✅ 基本对象操作正常');
                
                // 测试Promise
                Promise.resolve('test').then(result => {
                    console.log('✅ Promise 功能正常:', result);
                });
                
            } catch (error) {
                console.error('❌ Vue 核心测试失败:', error);
            }
        }

        function testModuleLoading() {
            console.log('🧪 测试模块加载...');
            
            try {
                // 测试动态导入（如果支持）
                if (typeof import === 'function') {
                    console.log('✅ 动态导入支持');
                } else {
                    console.log('ℹ️ 动态导入不支持（可能正常）');
                }
                
                // 测试模块系统
                if (typeof module !== 'undefined') {
                    console.log('✅ CommonJS 模块系统');
                } else if (typeof window !== 'undefined') {
                    console.log('✅ 浏览器环境正常');
                }
                
            } catch (error) {
                console.error('❌ 模块加载测试失败:', error);
            }
        }

        function testCircularDeps() {
            console.log('🧪 测试循环依赖检测...');
            
            try {
                // 创建一个简单的循环引用测试
                const objA = { name: 'A' };
                const objB = { name: 'B' };
                objA.ref = objB;
                objB.ref = objA;
                
                console.log('✅ 循环引用对象创建成功');
                
                // 测试JSON序列化（会失败，但不应该崩溃）
                try {
                    JSON.stringify(objA);
                    console.log('⚠️ 意外：循环引用对象可以序列化');
                } catch (e) {
                    console.log('✅ 循环引用检测正常（JSON序列化失败是预期的）');
                }
                
            } catch (error) {
                console.error('❌ 循环依赖测试失败:', error);
            }
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            console.log('🚀 页面加载完成，开始自动检查...');
            checkBuildFiles();
            
            // 检查是否有JavaScript错误
            window.addEventListener('error', (event) => {
                console.error('❌ JavaScript 错误:', event.error);
                console.error('文件:', event.filename, '行号:', event.lineno);
            });
            
            // 检查未处理的Promise拒绝
            window.addEventListener('unhandledrejection', (event) => {
                console.error('❌ 未处理的Promise拒绝:', event.reason);
            });
        });
    </script>
</body>
</html>
