-- =====================================================
-- 数据库迁移脚本：添加 calculated_score 字段
-- 创建时间：2024-07-04
-- 描述：在 fyfc_evaluations 表中添加 calculated_score 字段
-- =====================================================

-- 1. 检查字段是否已存在
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'fyfc_evaluations' 
  AND COLUMN_NAME = 'calculated_score';

-- 2. 添加 calculated_score 字段（如果不存在）
ALTER TABLE `fyfc_evaluations` 
ADD COLUMN `calculated_score` decimal(6,2) DEFAULT NULL COMMENT '最终计算得分' 
AFTER `score`;

-- 3. 重新创建视图以包含新字段
DROP VIEW IF EXISTS `v_evaluation_summary`;
CREATE VIEW `v_evaluation_summary` AS
SELECT
    e.id,
    e.department,
    e.name,
    e.review_date,
    e.colleague_name,
    e.manager_name,
    e.additional_score,
    e.score as final_score,
    e.calculated_score,
    e.comment,
    e.status,
    e.created_at,
    e.created_by,
    e.updated_at,
    e.updated_by,
    -- 员工自评得分
    (SELECT es.score FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id AND es.type = 'employee' LIMIT 1) as employee_score,
    -- 同事评价得分
    (SELECT es.score FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id AND es.type = 'colleague' LIMIT 1) as colleague_score,
    -- 主管评价得分
    (SELECT es.score FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id AND es.type = 'manager' LIMIT 1) as manager_score,
    -- 评价人数统计
    (SELECT COUNT(*) FROM fyfc_evaluation_scores es WHERE es.evaluation_id = e.id) as evaluator_count
FROM fyfc_evaluations e;

-- 4. 验证字段添加成功
DESCRIBE `fyfc_evaluations`;

-- 5. 验证视图更新成功
DESCRIBE `v_evaluation_summary`;

-- =====================================================
-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. calculated_score 字段允许为 NULL，可以后续通过业务逻辑填充
-- 3. 如果字段已存在，ALTER TABLE 语句会报错，这是正常的
-- =====================================================
