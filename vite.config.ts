import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'url';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';

export default defineConfig({
  base: '/vfyg',
  plugins: [
    vue(),
    AutoImport({
      imports: [
        'vue',
        {
          'naive-ui': [
            'useDialog',
            'useMessage',
            'useNotification',
            'useLoadingBar'
          ]
        }
      ],
      dts: './auto-imports.d.ts',
    }),
    Components({
      resolvers: [NaiveUiResolver()],
      dts: './components.d.ts',
    })
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 7002,
    strictPort: true,
    open: true,
    proxy: {
      '/aliapi/': {
        target: 'https://api.fyg.cn/',
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/aliapi/, ''),
      },
      '/api': {
        target: 'http://localhost:4000',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      '/fyjs': {
        target: 'https://data.fyg.cn/fyjs/',
        // target: 'http://localhost:7001/fyjs/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fyjs/, ''),
      },
      '/fyschedule': {
        // target: 'https://data.fyg.cn/fyschedule/',
        target: 'https://wx.fyg.cn:7001/fyschedule2/',
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fyschedule/, ''),
      },
    },
  },
  // build: {
  //   target: 'esnext',
  //   outDir: 'dist',
  //   assetsDir: 'assets',
  //   sourcemap: false,
  //   minify: 'terser',
  //   chunkSizeWarningLimit: 500,
  //   rollupOptions: {
  //     output: {
  //       manualChunks(id) {
  //         if (id.includes('node_modules')) {
  //           return id.toString().split('node_modules/')[1].split('/')[0].toString();
  //         }
  //       }
  //     }
  //   }
  // }
  // build打包构建配置
  build: {
    chunkSizeWarningLimit: 1500, // 适当提高阈值，避免过度分包
    // 打包输出的文件夹名称
    outDir: 'dist',
    // 静态资源文件保存的文件夹名称
    assetsDir: 'static',
    // 是否启用css代码拆分
    cssCodeSplit: true,
    // 打包构建后是否生成 source map 文件。
    sourcemap: false, // 生产环境关闭sourcemap减少体积
    // 打包构建时压缩混淆使用的混淆器
    minify: 'esbuild',
    // 构建目标，确保兼容性
    target: 'es2015',
    // 自定义底层的 Rollup 打包配置
    rollupOptions: {
      // 确保模块加载顺序
      external: [],
      // 输出配置
      output: {
        // 输出的文件自定义命名
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
        // 确保模块间的依赖关系正确
        globals: {},
        // 手动分包策略
        manualChunks(id) {
          // Vue 核心库 - 保持完整性，避免循环依赖
          if (id.includes('vue') && id.includes('node_modules') && !id.includes('vue-cookies') && !id.includes('vue-draggable')) {
            return 'vue-vendor';
          }
          if (id.includes('vue-router')) {
            return 'vue-vendor'; // 与Vue核心放在一起
          }

          // Naive UI 组件库 - 保持相对完整，只做基本分离
          if (id.includes('naive-ui')) {
            // 只分离数据表格，其他保持在一起
            if (id.includes('data-table') || id.includes('table')) {
              return 'naive-table';
            }
            return 'naive-ui';
          }
          if (id.includes('vfonts')) {
            return 'naive-ui';
          }

          // 图标库
          if (id.includes('@vicons')) {
            return 'icons';
          }

          // 基础工具库
          if (id.includes('axios') || id.includes('vue-cookies') ||
              id.includes('crypto-js') || id.includes('qs') || id.includes('uuid')) {
            return 'utils-base';
          }

          // 富文本编辑器 (ProseMirror)
          if (id.includes('prosemirror')) {
            return 'editor';
          }

          // Excel处理库
          if (id.includes('xlsx')) {
            return 'excel';
          }

          // 拖拽库
          if (id.includes('vue-draggable-plus')) {
            return 'draggable';
          }

          // FYFC 评价系统
          if (id.includes('/fyfc/review') ||
              id.includes('FyfcReviewApi') ||
              id.includes('FyfcOssService')) {
            return 'fyfc-review';
          }

          // FYG 会计系统
          if (id.includes('/fyg/accounting')) {
            return 'fyg-accounting';
          }

          // FYJS 项目管理
          if (id.includes('/fyjs/project')) {
            return 'fyjs-project';
          }

          // 其他 node_modules 依赖
          if (id.includes('node_modules')) {
            return id.toString().split('node_modules/')[1].split('/')[0].toString();
          }
        }
      },
    },
  },
});
