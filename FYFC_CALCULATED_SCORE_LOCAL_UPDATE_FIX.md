# FYFC 评价系统 - calculatedScore 本地数据更新修复

## 🐛 问题描述

在管理员 dashboard 中点击"完成"按钮后，本地数据更新时 `calculatedScore` 字段显示错误，会被错误地赋值为 `score` 或其他值。刷新页面后数据显示正常，说明数据库中的数据是正确的，问题出现在前端本地数据更新逻辑中。

## 🔍 问题分析

### 根本原因

在 `tableData` 计算属性中，我们没有正确处理 `calculatedScore` 字段：

```typescript
// 问题代码
return {
    ...evaluation,
    employeeScore,
    colleagueScore,
    managerScore,
    managerEvaluator,
    totalScore: totalScore || 0
    // ❌ 缺少 calculatedScore 字段的处理
};
```

### 问题影响

1. **本地更新丢失** - 当 `handleComplete` 函数更新 `evaluationList.value[index].calculatedScore` 时
2. **计算属性覆盖** - `tableData` 计算属性重新计算时没有保留 `calculatedScore` 字段
3. **显示错误** - 表格显示的 `calculatedScore` 可能是 `undefined` 或其他错误值
4. **刷新正常** - 刷新页面后从服务器重新获取数据，显示正确

## ✅ 修复方案

### 1. 修复 tableData 计算属性

在主要的 `tableData` 计算属性中添加 `calculatedScore` 字段处理：

```typescript
return {
    ...evaluation,
    employeeScore,
    colleagueScore,
    managerScore,
    managerEvaluator,
    totalScore: totalScore || 0,
    // ✅ 保留原始的 calculatedScore，如果没有则使用 totalScore
    calculatedScore: evaluation.calculatedScore ?? (totalScore || 0)
};
```

### 2. 修复导出功能

在导出功能的数据处理中也添加相同的逻辑：

```typescript
return {
    ...evaluation,
    employeeScore,
    colleagueScore,
    managerScore,
    managerEvaluator,
    totalScore: totalScore || 0,
    // ✅ 保留原始的 calculatedScore，如果没有则使用 totalScore
    calculatedScore: evaluation.calculatedScore ?? (totalScore || 0)
};
```

## 🔧 修复逻辑说明

### 使用空值合并运算符 (??)

```typescript
evaluation.calculatedScore ?? (totalScore || 0)
```

这个表达式的逻辑：
1. 如果 `evaluation.calculatedScore` 存在且不为 `null` 或 `undefined`，使用它
2. 否则使用 `totalScore || 0` 作为后备值

### 为什么不直接使用 totalScore

- `calculatedScore` 是数据库中存储的最终得分，可能与前端计算的 `totalScore` 不同
- 当评价状态为 `completed` 时，应该优先显示数据库中的 `calculatedScore`
- 只有在 `calculatedScore` 不存在时，才使用前端计算的 `totalScore` 作为后备

## 🧪 测试场景

### 修复前的问题场景

1. 点击"完成"按钮
2. 状态更新成功，`calculatedScore` 被计算并保存到数据库
3. 本地 `evaluationList` 中的 `calculatedScore` 被更新
4. 但 `tableData` 计算属性没有保留这个值
5. 表格显示错误的 `calculatedScore`

### 修复后的正确流程

1. 点击"完成"按钮
2. 状态更新成功，`calculatedScore` 被计算并保存到数据库
3. 本地 `evaluationList` 中的 `calculatedScore` 被更新
4. `tableData` 计算属性正确保留 `calculatedScore` 值
5. 表格显示正确的 `calculatedScore`

## 📊 数据流图

```
handleComplete() 
    ↓
更新 evaluationList[index].calculatedScore
    ↓
tableData 计算属性重新计算
    ↓
保留 evaluation.calculatedScore (修复后)
    ↓
表格显示正确的 calculatedScore
```

## ⚠️ 注意事项

1. **数据优先级** - 数据库中的 `calculatedScore` 优先于前端计算的 `totalScore`
2. **向后兼容** - 对于没有 `calculatedScore` 的旧数据，使用 `totalScore` 作为后备
3. **类型安全** - 使用空值合并运算符确保类型安全
4. **一致性** - 确保表格显示和导出功能使用相同的逻辑

## 🚀 验证方法

1. **点击完成按钮** - 验证 `calculatedScore` 立即显示正确值
2. **刷新页面** - 验证刷新后数据保持一致
3. **导出功能** - 验证导出的 Excel 包含正确的 `calculatedScore`
4. **不同状态** - 验证不同评价状态下的显示正确性
