#!/usr/bin/env node

/**
 * 构建分析脚本
 * 用于分析打包后的文件大小和分布
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeDistFolder() {
    const distPath = path.join(path.dirname(__dirname), 'dist');
    
    if (!fs.existsSync(distPath)) {
        console.log('❌ dist 文件夹不存在，请先运行 npm run build');
        return;
    }

    console.log('📊 构建文件分析报告\n');
    console.log('=' * 50);

    // 分析 JS 文件
    const jsPath = path.join(distPath, 'js');
    if (fs.existsSync(jsPath)) {
        console.log('\n📦 JavaScript 文件:');
        const jsFiles = fs.readdirSync(jsPath)
            .filter(file => file.endsWith('.js'))
            .map(file => {
                const filePath = path.join(jsPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    size: stats.size,
                    formattedSize: formatBytes(stats.size)
                };
            })
            .sort((a, b) => b.size - a.size);

        jsFiles.forEach(file => {
            const sizeIndicator = file.size > 1024 * 1024 ? '🔴' : 
                                 file.size > 500 * 1024 ? '🟡' : '🟢';
            console.log(`  ${sizeIndicator} ${file.name}: ${file.formattedSize}`);
        });

        const totalJSSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
        console.log(`\n  📊 总计: ${formatBytes(totalJSSize)}`);

        // 检查是否有超过1MB的文件
        const largeFiles = jsFiles.filter(file => file.size > 1024 * 1024);
        if (largeFiles.length > 0) {
            console.log('\n⚠️  发现大文件 (>1MB):');
            largeFiles.forEach(file => {
                console.log(`    - ${file.name}: ${file.formattedSize}`);
            });
        } else {
            console.log('\n✅ 所有JS文件都小于1MB');
        }
    }

    // 分析 CSS 文件
    const cssPath = path.join(distPath, 'css');
    if (fs.existsSync(cssPath)) {
        console.log('\n🎨 CSS 文件:');
        const cssFiles = fs.readdirSync(cssPath)
            .filter(file => file.endsWith('.css'))
            .map(file => {
                const filePath = path.join(cssPath, file);
                const stats = fs.statSync(filePath);
                return {
                    name: file,
                    size: stats.size,
                    formattedSize: formatBytes(stats.size)
                };
            })
            .sort((a, b) => b.size - a.size);

        cssFiles.forEach(file => {
            console.log(`  📄 ${file.name}: ${file.formattedSize}`);
        });

        const totalCSSSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
        console.log(`\n  📊 总计: ${formatBytes(totalCSSSize)}`);
    }

    // 分析其他静态资源
    const staticPath = path.join(distPath, 'static');
    if (fs.existsSync(staticPath)) {
        console.log('\n📁 静态资源:');
        
        function analyzeFolder(folderPath, prefix = '') {
            const items = fs.readdirSync(folderPath);
            items.forEach(item => {
                const itemPath = path.join(folderPath, item);
                const stats = fs.statSync(itemPath);
                
                if (stats.isDirectory()) {
                    console.log(`  📁 ${prefix}${item}/`);
                    analyzeFolder(itemPath, prefix + '  ');
                } else {
                    console.log(`  📄 ${prefix}${item}: ${formatBytes(stats.size)}`);
                }
            });
        }
        
        analyzeFolder(staticPath);
    }

    console.log('\n' + '=' * 50);
    console.log('✅ 分析完成！');
    
    // 给出优化建议
    console.log('\n💡 优化建议:');
    console.log('  1. 确保所有JS文件都小于1MB');
    console.log('  2. 使用gzip压缩可以进一步减少文件大小');
    console.log('  3. 考虑使用CDN加速静态资源加载');
    console.log('  4. 定期清理未使用的依赖和代码');
}

// 运行分析
analyzeDistFolder();
