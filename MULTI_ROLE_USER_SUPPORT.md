# 多角色用户支持文档

## 概述

本文档说明了FYFC评价系统对同时拥有多种权限用户的支持，特别是同时拥有 `EMPLOYEE` 和 `MANAGER` 权限的用户。

## 问题背景

在原有的系统中，用户权限处理采用单一角色优先级模式：
- 如果用户同时拥有 `EMPLOYEE` 和 `MANAGER` 权限
- 系统只会识别优先级更高的 `MANAGER` 角色
- 导致用户无法创建自己的评价并进行自评

## 解决方案

### 1. UserContext.ts 更新

#### 新增接口字段
```typescript
export interface UserInfo {
  // ... 其他字段
  role?: string; // 主要角色（保持向后兼容）
  roles?: string[]; // 所有角色列表
  // ... 其他字段
}
```

#### 新增函数
```typescript
/**
 * 获取用户的所有角色
 */
function getUserRoles(permissions: string[]): string[] {
  const roles: string[] = [];
  
  if (permissions.includes(PERMISSION_CODES.ADMIN)) {
    roles.push('admin');
  }
  if (permissions.includes(PERMISSION_CODES.MANAGER)) {
    roles.push('manager');
  }
  if (permissions.includes(PERMISSION_CODES.EMPLOYEE)) {
    roles.push('employee');
  }
  
  return roles.length > 0 ? roles : ['unknown'];
}
```

#### 新增 Hook 方法
```typescript
// 检查是否拥有指定的多个角色
const hasRoles = (roleList: string[]): boolean => {
  if (!currentUser.value?.roles) return false;
  return roleList.every(role => currentUser.value!.roles!.includes(role));
};

// 检查是否拥有指定角色中的任意一个
const hasAnyRole = (roleList: string[]): boolean => {
  if (!currentUser.value?.roles) return false;
  return roleList.some(role => currentUser.value!.roles!.includes(role));
};

// 获取用户的所有角色
const getUserAllRoles = (): string[] => {
  return currentUser.value?.roles || [];
};

// 检查是否同时拥有员工和管理者权限
const isEmployeeManager = computed(() => hasRoles(['employee', 'manager']));
```

### 2. 角色判断逻辑更新

#### 多角色处理优先级
1. **Admin 角色**：最高优先级，拥有所有权限
2. **Employee + Manager 角色**：
   - 创建新评价时：使用 `employee` 角色（自评）
   - 编辑自己的评价时：使用 `employee` 角色
   - 编辑他人的评价时：
     - **当前用户 = evaluation.colleagueName**：使用 `colleague` 角色（同事评价）
     - **当前用户 = evaluation.managerName**：使用 `manager` 角色（管理者评价）
     - **都不匹配**：默认使用 `manager` 角色（向后兼容）
3. **单一角色**：按原有逻辑处理

#### 实现示例
```typescript
// 获取用户的所有角色
const allRoles = getUserAllRoles();
const hasEmployeeRole = allRoles.includes('employee');
const hasManagerRole = allRoles.includes('manager');
const hasAdminRole = allRoles.includes('admin');

// 多角色处理逻辑
if (hasAdminRole) {
    roleType = 'admin';
    canEdit = true;
} else if (hasManagerRole && hasEmployeeRole) {
    // 同时拥有管理者和员工权限的用户
    if (!evaluationId.value) {
        // 新增模式：默认为本人自评（员工角色）
        roleType = 'employee';
        canEdit = true;
    } else if (evaluationData.value.name) {
        // 编辑模式且数据已加载：根据name和邀请字段判断
        if (currentUser === evaluationData.value.name) {
            roleType = 'employee'; // 本人，可以自评
            canEdit = true;
        } else {
            // 他人的评价：根据evaluation中的邀请字段判断角色
            if (currentUser === evaluationData.value.colleagueName) {
                roleType = 'colleague'; // 当前用户是被邀请的同事
                canEdit = true;
            } else if (currentUser === evaluationData.value.managerName) {
                roleType = 'manager'; // 当前用户是被邀请的主管
                canEdit = true;
            } else {
                // 如果都不匹配，默认为manager（向后兼容）
                roleType = 'manager';
                canEdit = true;
            }
        }
    }
}
```

### 3. API调用策略

对于多角色用户，采用智能API选择策略：

```typescript
if (hasManagerRole && hasEmployeeRole) {
    // 同时拥有管理者和员工权限的用户
    // 优先尝试使用staff API（因为可能是自己的评价）
    try {
        response = await fyfcReviewApi.staff.getEvaluationDetail(parseInt(evaluationId.value), currentUser);
    } catch (error) {
        console.log('staff API失败，尝试manager API:', error);
        // 如果staff API失败，尝试manager API
        response = await fyfcReviewApi.manager.getEvaluationDetail(parseInt(evaluationId.value), currentUser);
    }
}
```

## 使用方法

### 1. 检查用户是否为多角色用户
```typescript
const { isEmployeeManager, getUserAllRoles, hasRoles } = useUserContext();

// 方法1：使用计算属性
if (isEmployeeManager.value) {
    console.log('用户同时拥有员工和管理者权限');
}

// 方法2：使用函数检查
if (hasRoles(['employee', 'manager'])) {
    console.log('用户同时拥有员工和管理者权限');
}

// 方法3：获取所有角色
const allRoles = getUserAllRoles();
console.log('用户的所有角色:', allRoles);
```

### 2. 根据角色动态调整UI
```typescript
const showEmployeeActions = computed(() => {
    const roles = getUserAllRoles();
    return roles.includes('employee');
});

const showManagerActions = computed(() => {
    const roles = getUserAllRoles();
    return roles.includes('manager');
});
```

## 测试场景

### 场景1：多角色用户创建自己的评价
1. 用户同时拥有 `EMPLOYEE` 和 `MANAGER` 权限
2. 访问创建评价页面
3. 系统识别为 `employee` 角色
4. 用户可以创建自己的评价并进行自评

### 场景2：多角色用户作为被邀请同事评价他人
1. 用户同时拥有 `EMPLOYEE` 和 `MANAGER` 权限
2. 用户被设置为某个评价的 `colleagueName`
3. 访问该评价页面，系统识别为 `colleague` 角色
4. 用户可以进行同事评价

### 场景3：多角色用户作为被邀请主管评价他人
1. 用户同时拥有 `EMPLOYEE` 和 `MANAGER` 权限
2. 用户被设置为某个评价的 `managerName`
3. 访问该评价页面，系统识别为 `manager` 角色
4. 用户可以进行管理者评价

### 场景4：API调用容错
1. 多角色用户访问评价详情
2. 系统首先尝试 `staff` API
3. 如果失败，自动切换到 `manager` API
4. 确保用户能够正常访问数据

## 向后兼容性

- 保留原有的 `role` 字段，确保现有代码不受影响
- 新增的 `roles` 字段为可选字段
- 原有的单角色检查方法继续有效
- 新增的多角色检查方法为增强功能

## 注意事项

1. **权限验证**：多角色用户在不同场景下使用不同角色，需要确保后端API支持相应的权限验证
2. **数据一致性**：用户在不同角色下操作同一评价时，需要保证数据的一致性
3. **审计日志**：记录用户在不同角色下的操作，便于审计和追踪
4. **性能考虑**：API容错机制可能增加请求次数，需要监控性能影响

## 后续优化建议

1. **后端API统一**：考虑提供统一的API接口，根据用户权限自动返回相应数据
2. **角色切换UI**：为多角色用户提供角色切换界面，让用户主动选择当前使用的角色
3. **权限缓存**：缓存用户权限信息，减少重复的权限检查请求
4. **错误处理**：完善API调用失败时的错误处理和用户提示
